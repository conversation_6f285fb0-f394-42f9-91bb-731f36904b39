<script src="https://cdn.plot.ly/plotly-2.6.3.min.js"></script>    <div id="dcec8e5a_612d_439d_985c_82248b99a795" style="width:1200px;height:800px;"></div>
    <script>
    function plots_jl_plotly_dcec8e5a_612d_439d_985c_82248b99a795() {
        
        Plotly.newPlot('dcec8e5a_612d_439d_985c_82248b99a795', [
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738773.0,
            738773.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            27.14106810196658,
            29.335522077345043
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738773.0,
            738773.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            27.99999999999998,
            28.85714285714284
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738780.0,
            738780.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            26.83629632212626,
            28.153736633517198
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738780.0,
            738780.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            26.99999999999998,
            27.857142857142836
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738787.0,
            738787.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            25.33975302415375,
            28.00136162472535
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738787.0,
            738787.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            25.99999999999998,
            26.857142857142836
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738794.0,
            738794.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            24.599980669448623,
            26.03123268853665
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738794.0,
            738794.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            24.99999999999998,
            25.857142857142836
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738801.0,
            738801.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            24.037899513875935,
            27.58156715879359
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738801.0,
            738801.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            24.857142857142836,
            26.99999999999998
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738808.0,
            738808.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            25.057114116497292,
            27.402517411882258
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738808.0,
            738808.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            25.99999999999998,
            26.857142857142836
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738815.0,
            738815.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            24.81366337006599,
            28.96517154097577
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738815.0,
            738815.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            25.857142857142836,
            27.99999999999998
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738822.0,
            738822.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            26.67554194712479,
            28.99825395698268
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738822.0,
            738822.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            27.857142857142836,
            27.999999999999982
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738829.0,
            738829.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            26.8267971534251,
            29.36863168148921
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738829.0,
            738829.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            27.85714285714284,
            28.999999999999982
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738836.0,
            738836.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            27.334370234050624,
            29.243798182430258
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738836.0,
            738836.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            27.999999999999982,
            28.85714285714284
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738843.0,
            738843.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            27.52039109949827,
            29.48121780686753
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738843.0,
            738843.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            27.85714285714284,
            28.99999999999998
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738850.0,
            738850.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            28.15994403036025,
            30.486191523942374
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738850.0,
            738850.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            28.99999999999998,
            29.85714285714284
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738857.0,
            738857.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            27.4696842307819,
            29.61331380745388
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738857.0,
            738857.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            27.99999999999998,
            28.857142857142836
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738864.0,
            738864.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            25.854172827794866,
            28.270937786427307
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738864.0,
            738864.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            26.99999999999998,
            27.857142857142836
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738871.0,
            738871.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            26.2153013542783,
            28.543195836572565
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738871.0,
            738871.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            26.857142857142836,
            27.999999999999982
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738878.0,
            738878.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            26.0201714691854,
            28.50147878766875
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738878.0,
            738878.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            26.999999999999982,
            27.85714285714284
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738885.0,
            738885.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            25.17333669601776,
            27.227504422378097
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738885.0,
            738885.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            25.999999999999982,
            26.85714285714284
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738892.0,
            738892.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            24.61433997890067,
            26.761574706803593
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738892.0,
            738892.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            25.85714285714284,
            25.999999999999982
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738899.0,
            738899.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            24.376734800777328,
            26.580246602107124
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738899.0,
            738899.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            24.999999999999982,
            25.85714285714284
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738906.0,
            738906.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            23.26341124434416,
            25.59257771310869
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738906.0,
            738906.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            23.999999999999982,
            24.85714285714284
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738913.0,
            738913.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            22.708918102767484,
            24.242976193666056
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738913.0,
            738913.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            22.99999999999998,
            23.85714285714284
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738920.0,
            738920.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            21.90833936803605,
            24.324101656626695
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738920.0,
            738920.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            22.857142857142836,
            22.99999999999998
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738927.0,
            738927.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            21.384197819271183,
            23.66085323712385
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738927.0,
            738927.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            21.999999999999975,
            22.857142857142836
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738934.0,
            738934.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            20.56969925180197,
            21.915622786652076
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738934.0,
            738934.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            20.99999999999997,
            21.857142857142833
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738941.0,
            738941.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            20.547251393022428,
            21.97249781422294
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738941.0,
            738941.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            20.85714285714283,
            20.99999999999997
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738948.0,
            738948.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            19.986209605906854,
            21.91155497856303
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738948.0,
            738948.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            20.857142857142826,
            20.999999999999968
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738955.0,
            738955.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            19.93062374561699,
            21.756943615506184
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738955.0,
            738955.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            20.857142857142826,
            20.99999999999997
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738962.0,
            738962.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            20.317573470051503,
            21.9539117926768
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738962.0,
            738962.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            20.857142857142826,
            20.99999999999997
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738969.0,
            738969.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            20.80984566521455,
            24.1508418118479
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738969.0,
            738969.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            21.857142857142826,
            22.99999999999997
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738976.0,
            738976.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            21.713042105051695,
            23.893932584487086
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738976.0,
            738976.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            22.85714285714283,
            22.99999999999997
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738983.0,
            738983.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            21.954387894634582,
            23.238602840317423
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738983.0,
            738983.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            22.85714285714283,
            22.99999999999997
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738990.0,
            738990.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            21.934693962775114,
            24.69379749821638
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738990.0,
            738990.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            22.85714285714283,
            23.99999999999997
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738997.0,
            738997.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            23.26182861986454,
            24.822364269331295
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738997.0,
            738997.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            23.85714285714283,
            23.99999999999997
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739004.0,
            739004.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            22.964163437928896,
            24.41008085523969
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739004.0,
            739004.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            22.99999999999997,
            23.85714285714283
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739011.0,
            739011.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            21.20805588238184,
            25.11558662846534
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739011.0,
            739011.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            22.85714285714283,
            24.99999999999997
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739018.0,
            739018.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            23.910744706167748,
            25.777083544140357
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739018.0,
            739018.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            24.85714285714283,
            24.999999999999975
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739025.0,
            739025.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            23.527023674723907,
            25.02460658803639
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739025.0,
            739025.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            23.999999999999975,
            24.857142857142833
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739032.0,
            739032.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            22.309246063667658,
            24.499253582345013
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739032.0,
            739032.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            22.999999999999975,
            23.857142857142833
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739039.0,
            739039.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            22.440215995623273,
            24.2946369240675
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739039.0,
            739039.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            22.857142857142833,
            22.999999999999975
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739046.0,
            739046.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            22.525610327538644,
            24.277256810626895
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739046.0,
            739046.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            22.999999999999975,
            23.857142857142833
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739053.0,
            739053.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            21.7967242413495,
            23.157444637968158
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739053.0,
            739053.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            21.99999999999998,
            22.857142857142833
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739060.0,
            739060.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            20.78743673401614,
            22.386749531041044
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739060.0,
            739060.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            20.99999999999998,
            21.857142857142833
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739067.0,
            739067.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            19.822910258278466,
            21.56000117379388
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739067.0,
            739067.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            20.857142857142833,
            20.999999999999975
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739074.0,
            739074.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            19.867343063079524,
            21.55286456007532
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739074.0,
            739074.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            19.999999999999975,
            20.857142857142833
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739081.0,
            739081.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            19.191292744792477,
            20.84060975631825
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739081.0,
            739081.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            19.857142857142833,
            19.99999999999997
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739088.0,
            739088.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            18.866500503732492,
            20.354525278718462
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739088.0,
            739088.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            18.999999999999968,
            19.85714285714283
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739095.0,
            739095.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            19.060768827857814,
            22.80166029957668
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739095.0,
            739095.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            19.857142857142826,
            21.999999999999968
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739102.0,
            739102.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            20.57706609781379,
            22.60614478352923
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739102.0,
            739102.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            20.999999999999968,
            21.857142857142826
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739109.0,
            739109.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            20.91521292714041,
            22.274076230972835
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739109.0,
            739109.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            20.999999999999968,
            21.857142857142826
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739116.0,
            739116.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            19.879292913127415,
            21.828568159318102
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739116.0,
            739116.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            20.857142857142826,
            20.999999999999968
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739123.0,
            739123.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            19.461572759025863,
            21.234585108818475
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739123.0,
            739123.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            19.999999999999968,
            20.857142857142826
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739130.0,
            739130.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            18.64183303101367,
            20.257914061191382
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739130.0,
            739130.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            18.999999999999968,
            19.857142857142826
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739137.0,
            739137.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            18.79303609449144,
            20.11362872241937
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739137.0,
            739137.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            18.857142857142826,
            18.999999999999968
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739144.0,
            739144.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            19.703273585567494,
            21.805109105885485
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739144.0,
            739144.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            19.857142857142826,
            20.999999999999968
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739151.0,
            739151.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            19.44888406824885,
            21.745758127039572
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739151.0,
            739151.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            20.857142857142826,
            20.999999999999968
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739158.0,
            739158.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            19.808608537517532,
            21.387086770814584
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739158.0,
            739158.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            19.99999999999997,
            20.857142857142826
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739165.0,
            739165.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            18.764351101903877,
            20.367871165342695
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739165.0,
            739165.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            18.99999999999997,
            19.857142857142826
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739172.0,
            739172.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            18.049434953489868,
            20.28179059109819
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739172.0,
            739172.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            18.857142857142826,
            19.999999999999968
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739179.0,
            739179.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            18.961299739234178,
            21.568357495461797
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739179.0,
            739179.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            19.857142857142826,
            20.99999999999997
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739186.0,
            739186.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            20.59276017453912,
            23.747027358030135
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739186.0,
            739186.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            20.857142857142826,
            22.99999999999997
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739193.0,
            739193.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            22.471178237465235,
            24.02280253179779
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739193.0,
            739193.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            22.857142857142826,
            22.999999999999968
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739200.0,
            739200.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            22.29366081537941,
            23.549305636283044
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739200.0,
            739200.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            22.857142857142826,
            22.999999999999968
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739207.0,
            739207.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            21.935061029855337,
            24.795832478602673
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739207.0,
            739207.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            22.999999999999964,
            23.857142857142826
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739214.0,
            739214.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            22.390989129469855,
            24.572789830859545
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739214.0,
            739214.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            22.857142857142822,
            23.999999999999964
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739221.0,
            739221.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            22.96623149198693,
            25.57659707235258
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739221.0,
            739221.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            23.85714285714282,
            23.99999999999996
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739228.0,
            739228.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            22.792924967463517,
            24.59328831082782
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739228.0,
            739228.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            22.999999999999957,
            23.85714285714282
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739235.0,
            739235.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            21.859323696731376,
            23.421653682861184
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739235.0,
            739235.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            22.857142857142815,
            22.999999999999957
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739242.0,
            739242.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            22.667671571424858,
            24.583149775209915
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739242.0,
            739242.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            23.857142857142815,
            23.999999999999957
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739249.0,
            739249.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            23.00612032270065,
            25.047467569445306
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739249.0,
            739249.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            23.857142857142815,
            23.999999999999957
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739256.0,
            739256.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            23.900403739110963,
            25.904679723585016
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739256.0,
            739256.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            24.857142857142815,
            24.999999999999957
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739263.0,
            739263.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            24.818969313274412,
            26.882142947664473
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739263.0,
            739263.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            25.857142857142815,
            25.999999999999957
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739270.0,
            739270.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            24.296300376649015,
            26.244857670585432
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739270.0,
            739270.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            24.999999999999957,
            25.857142857142815
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739277.0,
            739277.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            23.16530735852296,
            25.680781054882583
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739277.0,
            739277.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            23.999999999999957,
            24.857142857142815
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739284.0,
            739284.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            22.676777021258438,
            24.319844758448475
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739284.0,
            739284.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            22.999999999999957,
            23.857142857142815
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739291.0,
            739291.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            22.024065125488043,
            24.43663905737004
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739291.0,
            739291.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            22.999999999999957,
            23.857142857142815
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739298.0,
            739298.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            21.652198034247558,
            23.39388703727439
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739298.0,
            739298.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            21.999999999999957,
            22.857142857142815
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739305.0,
            739305.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            19.96089417643215,
            22.107825481688273
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739305.0,
            739305.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            20.99999999999996,
            21.857142857142815
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739312.0,
            739312.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            19.725362772283194,
            21.368259206491995
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739312.0,
            739312.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            19.99999999999996,
            20.85714285714282
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739319.0,
            739319.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            20.699193867203263,
            22.13041369113182
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739319.0,
            739319.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            20.85714285714282,
            20.999999999999957
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739326.0,
            739326.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            21.07446884419927,
            22.716813173633952
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739326.0,
            739326.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            21.857142857142815,
            21.999999999999957
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739333.0,
            739333.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            20.30285630920813,
            22.268756716076357
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739333.0,
            739333.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            20.999999999999957,
            21.857142857142815
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739340.0,
            739340.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            21.459792835773694,
            23.35371169220307
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739340.0,
            739340.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            21.857142857142815,
            22.99999999999996
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739347.0,
            739347.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            21.62353354299029,
            23.28369143697222
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739347.0,
            739347.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            21.99999999999996,
            22.85714285714282
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739354.0,
            739354.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            21.0459336984725,
            23.097677404556386
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739354.0,
            739354.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            21.85714285714282,
            21.99999999999996
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739361.0,
            739361.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            21.19194499310048,
            23.860249847308378
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739361.0,
            739361.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            21.85714285714282,
            22.99999999999996
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739368.0,
            739368.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            20.922242173818308,
            23.378471444645395
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739368.0,
            739368.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            21.99999999999996,
            22.85714285714282
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739375.0,
            739375.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            20.53783144614151,
            23.123396868424912
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739375.0,
            739375.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            20.99999999999996,
            21.85714285714282
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739382.0,
            739382.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            19.65389719638702,
            21.091596433535052
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739382.0,
            739382.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            19.99999999999996,
            20.85714285714282
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739389.0,
            739389.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            19.72857180697414,
            26.38697961059071
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739389.0,
            739389.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            19.85714285714282,
            25.99999999999996
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739396.0,
            739396.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            24.508970522119185,
            26.32064350671485
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739396.0,
            739396.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            24.99999999999996,
            25.85714285714282
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739403.0,
            739403.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            22.418867070305946,
            25.199278002431804
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739403.0,
            739403.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            23.99999999999996,
            24.85714285714282
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739410.0,
            739410.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            22.726270591628438,
            24.875938527182345
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739410.0,
            739410.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            22.99999999999996,
            23.85714285714282
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739417.0,
            739417.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            21.576969937261044,
            23.361673035131286
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739417.0,
            739417.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            21.99999999999996,
            22.85714285714282
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739424.0,
            739424.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            20.8390220512372,
            22.05403257242524
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739424.0,
            739424.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            20.99999999999996,
            21.85714285714282
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739431.0,
            739431.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            19.472626714581278,
            21.85764212458036
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739431.0,
            739431.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            20.85714285714282,
            20.999999999999957
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739438.0,
            739438.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            19.57690627092019,
            21.26801467706514
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739438.0,
            739438.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            19.999999999999957,
            20.857142857142815
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739445.0,
            739445.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            19.006742766377748,
            21.575738400125157
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739445.0,
            739445.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            19.85714285714281,
            20.999999999999954
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739452.0,
            739452.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            20.502862235601846,
            21.859458251353907
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739452.0,
            739452.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            20.85714285714281,
            20.99999999999995
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739459.0,
            739459.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            19.214439754797496,
            21.169306570657454
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739459.0,
            739459.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            19.99999999999995,
            20.857142857142808
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739464.0,
            739464.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            18.932500450839147,
            20.853393011250088
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739464.0,
            739464.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            19.857142857142804,
            20.28571428571423
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738771.0,
            738771.0,
            738775.0,
            738775.0,
            738771.0,
            738771.0
        ],
        "showlegend": true,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738778.0,
            738778.0,
            738782.0,
            738782.0,
            738778.0,
            738778.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738785.0,
            738785.0,
            738789.0,
            738789.0,
            738785.0,
            738785.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738792.0,
            738792.0,
            738796.0,
            738796.0,
            738792.0,
            738792.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738799.0,
            738799.0,
            738803.0,
            738803.0,
            738799.0,
            738799.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            3.0,
            0.0,
            0.0,
            3.0,
            3.0,
            3.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738806.0,
            738806.0,
            738810.0,
            738810.0,
            738806.0,
            738806.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738813.0,
            738813.0,
            738817.0,
            738817.0,
            738813.0,
            738813.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            3.0,
            0.0,
            0.0,
            3.0,
            3.0,
            3.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738820.0,
            738820.0,
            738824.0,
            738824.0,
            738820.0,
            738820.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738827.0,
            738827.0,
            738831.0,
            738831.0,
            738827.0,
            738827.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            2.0,
            0.0,
            0.0,
            2.0,
            2.0,
            2.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738834.0,
            738834.0,
            738838.0,
            738838.0,
            738834.0,
            738834.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738841.0,
            738841.0,
            738845.0,
            738845.0,
            738841.0,
            738841.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            2.0,
            0.0,
            0.0,
            2.0,
            2.0,
            2.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738848.0,
            738848.0,
            738852.0,
            738852.0,
            738848.0,
            738848.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738855.0,
            738855.0,
            738859.0,
            738859.0,
            738855.0,
            738855.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738862.0,
            738862.0,
            738866.0,
            738866.0,
            738862.0,
            738862.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738869.0,
            738869.0,
            738873.0,
            738873.0,
            738869.0,
            738869.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            2.0,
            0.0,
            0.0,
            2.0,
            2.0,
            2.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738876.0,
            738876.0,
            738880.0,
            738880.0,
            738876.0,
            738876.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738883.0,
            738883.0,
            738887.0,
            738887.0,
            738883.0,
            738883.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738890.0,
            738890.0,
            738894.0,
            738894.0,
            738890.0,
            738890.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738897.0,
            738897.0,
            738901.0,
            738901.0,
            738897.0,
            738897.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738904.0,
            738904.0,
            738908.0,
            738908.0,
            738904.0,
            738904.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738911.0,
            738911.0,
            738915.0,
            738915.0,
            738911.0,
            738911.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738918.0,
            738918.0,
            738922.0,
            738922.0,
            738918.0,
            738918.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738925.0,
            738925.0,
            738929.0,
            738929.0,
            738925.0,
            738925.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738932.0,
            738932.0,
            738936.0,
            738936.0,
            738932.0,
            738932.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738939.0,
            738939.0,
            738943.0,
            738943.0,
            738939.0,
            738939.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738946.0,
            738946.0,
            738950.0,
            738950.0,
            738946.0,
            738946.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738953.0,
            738953.0,
            738957.0,
            738957.0,
            738953.0,
            738953.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738960.0,
            738960.0,
            738964.0,
            738964.0,
            738960.0,
            738960.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738967.0,
            738967.0,
            738971.0,
            738971.0,
            738967.0,
            738967.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            3.0,
            0.0,
            0.0,
            3.0,
            3.0,
            3.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738974.0,
            738974.0,
            738978.0,
            738978.0,
            738974.0,
            738974.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738981.0,
            738981.0,
            738985.0,
            738985.0,
            738981.0,
            738981.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738988.0,
            738988.0,
            738992.0,
            738992.0,
            738988.0,
            738988.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            2.0,
            0.0,
            0.0,
            2.0,
            2.0,
            2.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738995.0,
            738995.0,
            738999.0,
            738999.0,
            738995.0,
            738995.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739002.0,
            739002.0,
            739006.0,
            739006.0,
            739002.0,
            739002.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739009.0,
            739009.0,
            739013.0,
            739013.0,
            739009.0,
            739009.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            3.0,
            0.0,
            0.0,
            3.0,
            3.0,
            3.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739016.0,
            739016.0,
            739020.0,
            739020.0,
            739016.0,
            739016.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739023.0,
            739023.0,
            739027.0,
            739027.0,
            739023.0,
            739023.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739030.0,
            739030.0,
            739034.0,
            739034.0,
            739030.0,
            739030.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739037.0,
            739037.0,
            739041.0,
            739041.0,
            739037.0,
            739037.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739044.0,
            739044.0,
            739048.0,
            739048.0,
            739044.0,
            739044.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739051.0,
            739051.0,
            739055.0,
            739055.0,
            739051.0,
            739051.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739058.0,
            739058.0,
            739062.0,
            739062.0,
            739058.0,
            739058.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739065.0,
            739065.0,
            739069.0,
            739069.0,
            739065.0,
            739065.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739072.0,
            739072.0,
            739076.0,
            739076.0,
            739072.0,
            739072.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739079.0,
            739079.0,
            739083.0,
            739083.0,
            739079.0,
            739079.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739086.0,
            739086.0,
            739090.0,
            739090.0,
            739086.0,
            739086.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739093.0,
            739093.0,
            739097.0,
            739097.0,
            739093.0,
            739093.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            4.0,
            0.0,
            0.0,
            4.0,
            4.0,
            4.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739100.0,
            739100.0,
            739104.0,
            739104.0,
            739100.0,
            739100.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739107.0,
            739107.0,
            739111.0,
            739111.0,
            739107.0,
            739107.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739114.0,
            739114.0,
            739118.0,
            739118.0,
            739114.0,
            739114.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739121.0,
            739121.0,
            739125.0,
            739125.0,
            739121.0,
            739121.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739128.0,
            739128.0,
            739132.0,
            739132.0,
            739128.0,
            739128.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739135.0,
            739135.0,
            739139.0,
            739139.0,
            739135.0,
            739135.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739142.0,
            739142.0,
            739146.0,
            739146.0,
            739142.0,
            739142.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            3.0,
            0.0,
            0.0,
            3.0,
            3.0,
            3.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739149.0,
            739149.0,
            739153.0,
            739153.0,
            739149.0,
            739149.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739156.0,
            739156.0,
            739160.0,
            739160.0,
            739156.0,
            739156.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739163.0,
            739163.0,
            739167.0,
            739167.0,
            739163.0,
            739163.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739170.0,
            739170.0,
            739174.0,
            739174.0,
            739170.0,
            739170.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            2.0,
            0.0,
            0.0,
            2.0,
            2.0,
            2.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739177.0,
            739177.0,
            739181.0,
            739181.0,
            739177.0,
            739177.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            2.0,
            0.0,
            0.0,
            2.0,
            2.0,
            2.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739184.0,
            739184.0,
            739188.0,
            739188.0,
            739184.0,
            739184.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            3.0,
            0.0,
            0.0,
            3.0,
            3.0,
            3.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739191.0,
            739191.0,
            739195.0,
            739195.0,
            739191.0,
            739191.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739198.0,
            739198.0,
            739202.0,
            739202.0,
            739198.0,
            739198.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739205.0,
            739205.0,
            739209.0,
            739209.0,
            739205.0,
            739205.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739212.0,
            739212.0,
            739216.0,
            739216.0,
            739212.0,
            739212.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            2.0,
            0.0,
            0.0,
            2.0,
            2.0,
            2.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739219.0,
            739219.0,
            739223.0,
            739223.0,
            739219.0,
            739219.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739226.0,
            739226.0,
            739230.0,
            739230.0,
            739226.0,
            739226.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739233.0,
            739233.0,
            739237.0,
            739237.0,
            739233.0,
            739233.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739240.0,
            739240.0,
            739244.0,
            739244.0,
            739240.0,
            739240.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            2.0,
            0.0,
            0.0,
            2.0,
            2.0,
            2.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739247.0,
            739247.0,
            739251.0,
            739251.0,
            739247.0,
            739247.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739254.0,
            739254.0,
            739258.0,
            739258.0,
            739254.0,
            739254.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            2.0,
            0.0,
            0.0,
            2.0,
            2.0,
            2.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739261.0,
            739261.0,
            739265.0,
            739265.0,
            739261.0,
            739261.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            2.0,
            0.0,
            0.0,
            2.0,
            2.0,
            2.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739268.0,
            739268.0,
            739272.0,
            739272.0,
            739268.0,
            739268.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739275.0,
            739275.0,
            739279.0,
            739279.0,
            739275.0,
            739275.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739282.0,
            739282.0,
            739286.0,
            739286.0,
            739282.0,
            739282.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739289.0,
            739289.0,
            739293.0,
            739293.0,
            739289.0,
            739289.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739296.0,
            739296.0,
            739300.0,
            739300.0,
            739296.0,
            739296.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739303.0,
            739303.0,
            739307.0,
            739307.0,
            739303.0,
            739303.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739310.0,
            739310.0,
            739314.0,
            739314.0,
            739310.0,
            739310.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739317.0,
            739317.0,
            739321.0,
            739321.0,
            739317.0,
            739317.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            2.0,
            0.0,
            0.0,
            2.0,
            2.0,
            2.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739324.0,
            739324.0,
            739328.0,
            739328.0,
            739324.0,
            739324.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            2.0,
            0.0,
            0.0,
            2.0,
            2.0,
            2.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739331.0,
            739331.0,
            739335.0,
            739335.0,
            739331.0,
            739331.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739338.0,
            739338.0,
            739342.0,
            739342.0,
            739338.0,
            739338.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            3.0,
            0.0,
            0.0,
            3.0,
            3.0,
            3.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739345.0,
            739345.0,
            739349.0,
            739349.0,
            739345.0,
            739345.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739352.0,
            739352.0,
            739356.0,
            739356.0,
            739352.0,
            739352.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739359.0,
            739359.0,
            739363.0,
            739363.0,
            739359.0,
            739359.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            2.0,
            0.0,
            0.0,
            2.0,
            2.0,
            2.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739366.0,
            739366.0,
            739370.0,
            739370.0,
            739366.0,
            739366.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739373.0,
            739373.0,
            739377.0,
            739377.0,
            739373.0,
            739373.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739380.0,
            739380.0,
            739384.0,
            739384.0,
            739380.0,
            739380.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739387.0,
            739387.0,
            739391.0,
            739391.0,
            739387.0,
            739387.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            7.0,
            0.0,
            0.0,
            7.0,
            7.0,
            7.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739394.0,
            739394.0,
            739398.0,
            739398.0,
            739394.0,
            739394.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739401.0,
            739401.0,
            739405.0,
            739405.0,
            739401.0,
            739401.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739408.0,
            739408.0,
            739412.0,
            739412.0,
            739408.0,
            739408.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739415.0,
            739415.0,
            739419.0,
            739419.0,
            739415.0,
            739415.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739422.0,
            739422.0,
            739426.0,
            739426.0,
            739422.0,
            739422.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739429.0,
            739429.0,
            739433.0,
            739433.0,
            739429.0,
            739429.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739436.0,
            739436.0,
            739440.0,
            739440.0,
            739436.0,
            739436.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739443.0,
            739443.0,
            739447.0,
            739447.0,
            739443.0,
            739443.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            2.0,
            0.0,
            0.0,
            2.0,
            2.0,
            2.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739450.0,
            739450.0,
            739454.0,
            739454.0,
            739450.0,
            739450.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739457.0,
            739457.0,
            739461.0,
            739461.0,
            739457.0,
            739457.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739462.0,
            739462.0,
            739466.0,
            739466.0,
            739462.0,
            739462.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "colorbar": {
            "y": 0.14547278269903766,
            "title": "",
            "len": 0.21513082349081364,
            "x": 0.9967191601049868
        },
        "yaxis": "y2",
        "x": [
            738773.0,
            738780.0,
            738787.0,
            738794.0,
            738801.0,
            738808.0,
            738815.0,
            738822.0,
            738829.0,
            738836.0,
            738843.0,
            738850.0,
            738857.0,
            738864.0,
            738871.0,
            738878.0,
            738885.0,
            738892.0,
            738899.0,
            738906.0,
            738913.0,
            738920.0,
            738927.0,
            738934.0,
            738941.0,
            738948.0,
            738955.0,
            738962.0,
            738969.0,
            738976.0,
            738983.0,
            738990.0,
            738997.0,
            739004.0,
            739011.0,
            739018.0,
            739025.0,
            739032.0,
            739039.0,
            739046.0,
            739053.0,
            739060.0,
            739067.0,
            739074.0,
            739081.0,
            739088.0,
            739095.0,
            739102.0,
            739109.0,
            739116.0,
            739123.0,
            739130.0,
            739137.0,
            739144.0,
            739151.0,
            739158.0,
            739165.0,
            739172.0,
            739179.0,
            739186.0,
            739193.0,
            739200.0,
            739207.0,
            739214.0,
            739221.0,
            739228.0,
            739235.0,
            739242.0,
            739249.0,
            739256.0,
            739263.0,
            739270.0,
            739277.0,
            739284.0,
            739291.0,
            739298.0,
            739305.0,
            739312.0,
            739319.0,
            739326.0,
            739333.0,
            739340.0,
            739347.0,
            739354.0,
            739361.0,
            739368.0,
            739375.0,
            739382.0,
            739389.0,
            739396.0,
            739403.0,
            739410.0,
            739417.0,
            739424.0,
            739431.0,
            739438.0,
            739445.0,
            739452.0,
            739459.0,
            739464.0
        ],
        "showlegend": false,
        "mode": "markers",
        "name": "成交量",
        "legendgroup": "成交量",
        "marker": {
            "symbol": "circle",
            "color": "rgba(51, 102, 178, 0.000)",
            "line": {
                "color": "rgba(0, 0, 0, 0.000)",
                "width": 1
            },
            "size": 0
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            3.0,
            0.0,
            3.0,
            1.0,
            2.0,
            0.0,
            2.0,
            1.0,
            0.0,
            0.0,
            2.0,
            0.0,
            0.0,
            1.0,
            0.0,
            0.0,
            0.0,
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0,
            1.0,
            3.0,
            1.0,
            1.0,
            2.0,
            1.0,
            0.0,
            3.0,
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            0.0,
            0.0,
            1.0,
            0.0,
            1.0,
            0.0,
            4.0,
            0.0,
            1.0,
            1.0,
            0.0,
            0.0,
            1.0,
            3.0,
            1.0,
            0.0,
            0.0,
            2.0,
            2.0,
            3.0,
            1.0,
            1.0,
            1.0,
            2.0,
            1.0,
            0.0,
            1.0,
            2.0,
            1.0,
            2.0,
            2.0,
            0.0,
            0.0,
            0.0,
            1.0,
            0.0,
            0.0,
            0.0,
            2.0,
            2.0,
            0.0,
            3.0,
            0.0,
            1.0,
            2.0,
            0.0,
            0.0,
            0.0,
            7.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            1.0,
            0.0,
            2.0,
            1.0,
            0.0,
            1.0
        ],
        "type": "scatter"
    }
]
, {
    "showlegend": true,
    "paper_bgcolor": "rgba(255, 255, 255, 1.000)",
    "xaxis1": {
        "showticklabels": true,
        "gridwidth": 1,
        "tickvals": [
            738886.0,
            739252.0
        ],
        "range": [
            738752.27,
            739484.73
        ],
        "domain": [
            0.05420676582093905,
            0.9967191601049868
        ],
        "mirror": false,
        "tickangle": 0,
        "showline": true,
        "ticktext": [
            "2024-01-01",
            "2025-01-01"
        ],
        "zeroline": false,
        "tickfont": {
            "color": "rgba(0, 0, 0, 1.000)",
            "family": "Microsoft YaHei, SimHei, Arial Unicode MS, sans-serif",
            "size": 14
        },
        "zerolinecolor": "rgba(0, 0, 0, 1.000)",
        "anchor": "y1",
        "visible": true,
        "ticks": "inside",
        "tickmode": "array",
        "linecolor": "rgba(0, 0, 0, 1.000)",
        "showgrid": true,
        "title": "日期",
        "gridcolor": "rgba(211, 211, 211, 0.300)",
        "titlefont": {
            "color": "rgba(0, 0, 0, 1.000)",
            "family": "Microsoft YaHei, SimHei, Arial Unicode MS, sans-serif",
            "size": 17
        },
        "tickcolor": "rgb(0, 0, 0)",
        "type": "-"
    },
    "legend": {
        "yanchor": "auto",
        "xanchor": "auto",
        "bordercolor": "rgba(0, 0, 0, 1.000)",
        "bgcolor": "rgba(255, 255, 255, 1.000)",
        "borderwidth": 1,
        "tracegroupgap": 0,
        "y": 1.0,
        "font": {
            "color": "rgba(0, 0, 0, 1.000)",
            "family": "Microsoft YaHei, SimHei, Arial Unicode MS, sans-serif",
            "size": 15
        },
        "title": {
            "font": {
                "color": "rgba(0, 0, 0, 1.000)",
                "family": "Microsoft YaHei, SimHei, Arial Unicode MS, sans-serif",
                "size": 15
            },
            "text": ""
        },
        "traceorder": "normal",
        "x": 1.0
    },
    "height": 800,
    "yaxis2": {
        "showticklabels": true,
        "gridwidth": 1,
        "tickvals": [
            0.0,
            1.0,
            2.0,
            3.0,
            4.0,
            5.0,
            6.0,
            7.0
        ],
        "range": [
            0.0,
            7.0
        ],
        "domain": [
            0.03790737095363082,
            0.2530381944444445
        ],
        "mirror": false,
        "tickangle": 0,
        "showline": true,
        "ticktext": [
            "0",
            "1",
            "2",
            "3",
            "4",
            "5",
            "6",
            "7"
        ],
        "zeroline": false,
        "tickfont": {
            "color": "rgba(0, 0, 0, 1.000)",
            "family": "Microsoft YaHei, SimHei, Arial Unicode MS, sans-serif",
            "size": 13
        },
        "zerolinecolor": "rgba(0, 0, 0, 1.000)",
        "anchor": "x2",
        "visible": true,
        "ticks": "inside",
        "tickmode": "array",
        "linecolor": "rgba(0, 0, 0, 1.000)",
        "showgrid": true,
        "title": "成交量",
        "gridcolor": "rgba(211, 211, 211, 0.300)",
        "titlefont": {
            "color": "rgba(0, 0, 0, 1.000)",
            "family": "Microsoft YaHei, SimHei, Arial Unicode MS, sans-serif",
            "size": 14
        },
        "tickcolor": "rgb(0, 0, 0)",
        "type": "-"
    },
    "xaxis2": {
        "showticklabels": true,
        "gridwidth": 1,
        "tickvals": [
            738886.0,
            739252.0
        ],
        "range": [
            738728.049,
            739508.951
        ],
        "domain": [
            0.05420676582093905,
            0.9967191601049868
        ],
        "mirror": false,
        "tickangle": 0,
        "showline": true,
        "ticktext": [
            "2024-01-01",
            "2025-01-01"
        ],
        "zeroline": false,
        "tickfont": {
            "color": "rgba(0, 0, 0, 1.000)",
            "family": "Microsoft YaHei, SimHei, Arial Unicode MS, sans-serif",
            "size": 13
        },
        "zerolinecolor": "rgba(0, 0, 0, 1.000)",
        "anchor": "y2",
        "visible": true,
        "ticks": "inside",
        "tickmode": "array",
        "linecolor": "rgba(0, 0, 0, 1.000)",
        "showgrid": true,
        "title": "日期",
        "gridcolor": "rgba(211, 211, 211, 0.300)",
        "titlefont": {
            "color": "rgba(0, 0, 0, 1.000)",
            "family": "Microsoft YaHei, SimHei, Arial Unicode MS, sans-serif",
            "size": 14
        },
        "tickcolor": "rgb(0, 0, 0)",
        "type": "-"
    },
    "yaxis1": {
        "showticklabels": true,
        "gridwidth": 1,
        "tickvals": [
            20.0,
            22.5,
            25.0,
            27.5,
            30.0
        ],
        "range": [
            17.676332256376295,
            30.85929422105595
        ],
        "domain": [
            0.3219084919072616,
            0.9673009623797024
        ],
        "mirror": false,
        "tickangle": 0,
        "showline": true,
        "ticktext": [
            "20.0",
            "22.5",
            "25.0",
            "27.5",
            "30.0"
        ],
        "zeroline": false,
        "tickfont": {
            "color": "rgba(0, 0, 0, 1.000)",
            "family": "Microsoft YaHei, SimHei, Arial Unicode MS, sans-serif",
            "size": 14
        },
        "zerolinecolor": "rgba(0, 0, 0, 1.000)",
        "anchor": "x1",
        "visible": true,
        "ticks": "inside",
        "tickmode": "array",
        "linecolor": "rgba(0, 0, 0, 1.000)",
        "showgrid": true,
        "title": "價格",
        "gridcolor": "rgba(211, 211, 211, 0.300)",
        "titlefont": {
            "color": "rgba(0, 0, 0, 1.000)",
            "family": "Microsoft YaHei, SimHei, Arial Unicode MS, sans-serif",
            "size": 17
        },
        "tickcolor": "rgb(0, 0, 0)",
        "type": "-"
    },
    "annotations": [
        {
            "yanchor": "top",
            "xanchor": "center",
            "rotation": -0.0,
            "y": 1.0,
            "font": {
                "color": "rgba(0, 0, 0, 1.000)",
                "family": "Microsoft YaHei, SimHei, Arial Unicode MS, sans-serif",
                "size": 22
            },
            "yref": "paper",
            "showarrow": false,
            "text": "fan5_01,07,36,09,39 週線 蠟燭圖",
            "xref": "paper",
            "x": 0.525462962962963
        },
        {
            "yanchor": "top",
            "xanchor": "center",
            "rotation": -0.0,
            "y": 0.27879278762029747,
            "font": {
                "color": "rgba(0, 0, 0, 1.000)",
                "family": "Microsoft YaHei, SimHei, Arial Unicode MS, sans-serif",
                "size": 17
            },
            "yref": "paper",
            "showarrow": false,
            "text": "成交量",
            "xref": "paper",
            "x": 0.525462962962963
        }
    ],
    "plot_bgcolor": "rgba(255, 255, 255, 1.000)",
    "margin": {
        "l": 0,
        "b": 20,
        "r": 0,
        "t": 20
    },
    "width": 1200
}
);
        
    }
    </script>
    <script src="https://requirejs.org/docs/release/2.3.7/minified/require.js" onload="plots_jl_plotly_dcec8e5a_612d_439d_985c_82248b99a795()"></script>
