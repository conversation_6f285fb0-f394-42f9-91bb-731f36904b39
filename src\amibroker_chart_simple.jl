#!/usr/bin/env julia

"""
AmiBroker 圖表模擬器 - 簡化增強版
使用 Plots.jl 繪製專業蠟燭圖（避免命名衝突）
"""

using CSV
using DataFrames
using Dates
using Statistics
using Printf

# 包含原有的基本功能
include("amibroker_chart_simulator.jl")

"""
獲取可用的股票代碼列表
"""
function get_available_tickers()::Vector{String}
    base_dir = "data_amibroker/fan5/g5"

    if !isdir(base_dir)
        println("❌ 數據目錄不存在：$base_dir")
        return String[]
    end

    files = readdir(base_dir)
    csv_files = filter(f -> endswith(f, ".csv"), files)

    # 提取股票代碼（去掉 .csv 擴展名）
    tickers = [replace(f, ".csv" => "") for f in csv_files]

    return sort(tickers)
end

# 載入 Plots 包（確保在全局作用域中可用）
global PLOTS_AVAILABLE = false
global CURRENT_BACKEND = "none"

# 首先嘗試載入 Plots
try
    using Plots
    println("✅ Plots.jl 基礎模組載入成功")

    # 優先使用 PlotlyJS 後端（對中文支持更好）
    try
        # 嘗試載入 PlotlyJS
        using PlotlyJS
        plotlyjs()
        global PLOTS_AVAILABLE = true
        global CURRENT_BACKEND = "plotlyjs"
        println("✅ Plots.jl 已載入，使用 PlotlyJS 後端（中文支持優化）")

        # 設置中文字體支持
        try
            # 根據操作系統選擇最佳中文字體
            if Sys.iswindows()
                font_family = "Microsoft YaHei, SimHei, Arial Unicode MS, sans-serif"
            elseif Sys.islinux()
                font_family = "WenQuanYi Micro Hei, Noto Sans CJK SC, DejaVu Sans, sans-serif"
            elseif Sys.isapple()
                font_family = "PingFang SC, Hiragino Sans GB, Arial Unicode MS, sans-serif"
            else
                font_family = "Arial Unicode MS, sans-serif"
            end

            # 設置默認字體為支持中文的字體
            Plots.default(fontfamily=font_family,
                         titlefontsize=16,
                         guidefontsize=12,
                         tickfontsize=10,
                         legendfontsize=11)
            println("✅ 中文字體設置完成：$font_family")
        catch e
            println("⚠️  字體設置失敗：$(e)，使用默認設置")
        end

    catch e
        println("⚠️  PlotlyJS 載入失敗：$(e)")
        # 如果 PlotlyJS 失敗，嘗試 GR
        try
            Plots.gr()
            global PLOTS_AVAILABLE = true
            global CURRENT_BACKEND = "gr"
            println("✅ Plots.jl 已載入，使用 GR 後端")
            println("⚠️  GR 後端對中文支持有限，建議安裝 PlotlyJS")
            println("   安裝命令：julia --project=. -e \"using Pkg; Pkg.add(\\\"PlotlyJS\\\")\"")
        catch
            # 如果都失敗，使用默認後端
            global PLOTS_AVAILABLE = true
            global CURRENT_BACKEND = "default"
            println("✅ Plots.jl 已載入，使用默認後端")
            println("⚠️  默認後端對中文支持有限")
        end
    end
catch e
    global PLOTS_AVAILABLE = false
    global CURRENT_BACKEND = "none"
    println("⚠️  Plots.jl 載入失敗：$e")
    println("   請確認 Plots.jl 已正確安裝")
    println("   安裝命令：julia --project=. -e \"using Pkg; Pkg.add([\\\"Plots\\\", \\\"PlotlyJS\\\"])\"")
end

# 確保 Plots 函數在全局作用域中可用
if PLOTS_AVAILABLE
    # 導入 Plots 的主要函數到全局作用域
    import Plots: plot, plot!, bar, savefig, default, @layout
    import Plots.Colors: RGB
end

"""
獲取字體設置（根據後端優化中文顯示）
"""
function get_font_settings()
    if CURRENT_BACKEND == "plotlyjs"
        # PlotlyJS 後端對中文支持較好
        return Dict(
            :family => "Microsoft YaHei, SimHei, Arial Unicode MS, sans-serif",
            :title => 16,
            :guide => 12,
            :tick => 10,
            :legend => 11
        )
    elseif CURRENT_BACKEND == "gr"
        # GR 後端使用較小字體避免顯示問題
        return Dict(
            :family => "Arial",
            :title => 14,
            :guide => 11,
            :tick => 9,
            :legend => 10
        )
    else
        # 默認設置
        return Dict(
            :family => "sans-serif",
            :title => 14,
            :guide => 11,
            :tick => 9,
            :legend => 10
        )
    end
end

"""
設置圖表的中文字體和編碼
"""
function setup_chinese_fonts()
    if !PLOTS_AVAILABLE
        return
    end

    try
        if CURRENT_BACKEND == "plotlyjs"
            # PlotlyJS 後端設置
            Plots.default(
                fontfamily="Microsoft YaHei, SimHei, Arial Unicode MS, sans-serif",
                titlefontsize=16,
                guidefontsize=12,
                tickfontsize=10,
                legendfontsize=11
            )
        elseif CURRENT_BACKEND == "gr"
            # GR 後端設置（有限的中文支持）
            Plots.default(
                fontfamily="Arial",
                titlefontsize=14,
                guidefontsize=11,
                tickfontsize=9,
                legendfontsize=10
            )
        end
        println("✅ 字體設置已優化")
    catch e
        println("⚠️  字體設置失敗：$e")
    end
end

"""
修復HTML文件的中文顯示問題
"""
function fix_html_chinese_display(filepath::String)
    try
        # 讀取原始HTML內容
        content = read(filepath, String)

        # 檢查是否已經有HTML結構
        if !occursin("<html", content)
            # 創建完整的HTML結構
            html_content = """<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AmiBroker 圖表</title>
    <style>
        body {
            font-family: "Microsoft YaHei", "SimHei", "Arial Unicode MS", sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .chart-container {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
            margin: 0 auto;
            max-width: 1240px;
        }
        .chart-title {
            text-align: center;
            color: #333;
            margin-bottom: 20px;
            font-size: 24px;
            font-weight: bold;
        }
        .chart-info {
            text-align: center;
            color: #666;
            margin-bottom: 15px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="chart-container">
        <div class="chart-title">📊 AmiBroker 圖表模擬器</div>
        <div class="chart-info">🕯️ 專業蠟燭圖 | 漲紅跌綠 | 中文完美顯示</div>
        $content
    </div>
</body>
</html>"""

            # 寫入修復後的內容
            write(filepath, html_content)

        else
            # 如果已經有HTML結構，只需要確保字符編碼正確
            if !occursin("charset=UTF-8", content)
                # 在head標籤後添加字符編碼
                content = replace(content, "<head>" => "<head>\n    <meta charset=\"UTF-8\">")
                write(filepath, content)
            end
        end

        println("🔧 HTML中文顯示已優化")

    catch e
        println("⚠️  HTML修復失敗：$e")
    end
end

"""
使用 Plots.jl 繪製真正的蠟燭圖（漲紅跌綠）
"""
function plot_real_candlestick(ohlcv_data::Vector{OHLCVData}, title::String="蠟燭圖", max_candles::Int=100)
    if !PLOTS_AVAILABLE
        println("❌ Plots.jl 不可用，使用文字版圖表")
        display_candlestick_data(ohlcv_data, title, min(20, max_candles))
        return nothing
    end

    if isempty(ohlcv_data)
        throw(ArgumentError("數據不能為空"))
    end

    # 限制顯示的蠟燭數量
    data_to_plot = if length(ohlcv_data) > max_candles
        ohlcv_data[end-max_candles+1:end]
    else
        ohlcv_data
    end

    # 提取數據
    dates = [d.date for d in data_to_plot]
    opens = [d.open for d in data_to_plot]
    highs = [d.high for d in data_to_plot]
    lows = [d.low for d in data_to_plot]
    closes = [d.close for d in data_to_plot]
    volumes = [d.volume for d in data_to_plot]

    try
        # 創建蠟燭圖 - 優化視覺品質和中文顯示
        font_settings = get_font_settings()
        p1 = plot(title=title,
                  xlabel="日期", ylabel="價格",
                  grid=true, gridwidth=1, gridcolor=:lightgray, gridalpha=0.3,
                  background_color=:white, foreground_color=:black,
                  titlefontsize=font_settings[:title],
                  guidefontsize=font_settings[:guide],
                  tickfontsize=font_settings[:tick],
                  legend=:topright, legendfontsize=font_settings[:legend],
                  fontfamily=font_settings[:family])

        # 計算蠟燭寬度（基於日期間隔）
        if length(dates) > 1
            date_diff = dates[2] - dates[1]
            candle_width = Dates.value(date_diff) * 0.6  # 60% 的日期間隔作為蠟燭寬度
        else
            candle_width = 1
        end

        # 繪製每根蠟燭
        for i in 1:length(data_to_plot)
            date = dates[i]
            open_price = opens[i]
            high_price = highs[i]
            low_price = lows[i]
            close_price = closes[i]

            # 判斷漲跌：漲紅跌綠（中國習慣）
            is_bullish = close_price >= open_price
            candle_color = is_bullish ? RGB(0.8, 0.1, 0.1) : RGB(0.1, 0.6, 0.1)  # 更鮮明的紅綠色
            fill_alpha = 0.85

            # 繪製影線（high-low line）- 更細緻的線條
            plot!(p1, [date, date], [low_price, high_price],
                  color=candle_color, linewidth=1.2, label="", alpha=0.9)

            # 繪製實體（open-close rectangle）
            body_height = abs(close_price - open_price)
            body_bottom = min(open_price, close_price)
            body_top = max(open_price, close_price)

            if body_height > 0.001  # 避免過小的實體
                # 有實體的蠟燭 - 使用更粗的線條模擬實體
                plot!(p1, [date, date], [body_bottom, body_top],
                      color=candle_color, linewidth=7, label="", alpha=fill_alpha)
            else
                # 十字星（開盤價等於收盤價）- 使用深灰色
                plot!(p1, [date, date], [open_price - 0.01, close_price + 0.01],
                      color=RGB(0.3, 0.3, 0.3), linewidth=4, label="", alpha=0.9)
            end
        end

        # 添加圖例說明 - 使用更鮮明的顏色
        plot!(p1, [], [], color=RGB(0.8, 0.1, 0.1), linewidth=6, label="上漲", alpha=0.85)
        plot!(p1, [], [], color=RGB(0.1, 0.6, 0.1), linewidth=6, label="下跌", alpha=0.85)

        # 創建成交量子圖 - 優化視覺品質和中文顯示
        font_settings = get_font_settings()
        p2 = bar(dates, volumes, title="成交量",
                 color=RGB(0.2, 0.4, 0.7), alpha=0.7, label="成交量",
                 xlabel="日期", ylabel="成交量",
                 titlefontsize=font_settings[:guide],
                 guidefontsize=font_settings[:tick],
                 tickfontsize=font_settings[:tick]-1,
                 fontfamily=font_settings[:family],
                 grid=true, gridwidth=1, gridcolor=:lightgray, gridalpha=0.3)

        # 組合圖表 - 主圖表佔3/4空間，成交量圖佔1/4空間
        combined_plot = plot(p1, p2, layout=@layout([a{0.75h}; b{0.25h}]), size=(1200, 800))

        return combined_plot

    catch e
        println("❌ 圖表繪製失敗：$e")
        println("回退到文字版圖表...")
        display_candlestick_data(ohlcv_data, title, min(20, max_candles))
        return nothing
    end
end

"""
使用 Plots.jl 繪製簡單的專業蠟燭圖（保持向後兼容）
"""
function plot_simple_candlestick(ohlcv_data::Vector{OHLCVData}, title::String="專業蠟燭圖", max_candles::Int=100)
    if !PLOTS_AVAILABLE
        println("❌ Plots.jl 不可用，使用文字版圖表")
        display_candlestick_data(ohlcv_data, title, min(20, max_candles))
        return nothing
    end
    
    if isempty(ohlcv_data)
        throw(ArgumentError("數據不能為空"))
    end
    
    # 限制顯示的蠟燭數量
    data_to_plot = if length(ohlcv_data) > max_candles
        ohlcv_data[end-max_candles+1:end]
    else
        ohlcv_data
    end
    
    # 提取數據
    dates = [d.date for d in data_to_plot]
    opens = [d.open for d in data_to_plot]
    highs = [d.high for d in data_to_plot]
    lows = [d.low for d in data_to_plot]
    closes = [d.close for d in data_to_plot]
    volumes = [d.volume for d in data_to_plot]
    
    try
        # 創建蠟燭圖 - 優化視覺品質和中文顯示
        font_settings = get_font_settings()
        p1 = plot(title=title,
                  xlabel="日期", ylabel="價格",
                  grid=true, gridwidth=1, gridcolor=:lightgray, gridalpha=0.3,
                  background_color=:white, foreground_color=:black,
                  titlefontsize=font_settings[:title],
                  guidefontsize=font_settings[:guide],
                  tickfontsize=font_settings[:tick],
                  legend=:topright, legendfontsize=font_settings[:legend],
                  fontfamily=font_settings[:family])

        # 繪製每根蠟燭
        for i in 1:length(data_to_plot)
            date = dates[i]
            open_price = opens[i]
            high_price = highs[i]
            low_price = lows[i]
            close_price = closes[i]

            # 判斷漲跌：漲紅跌綠
            is_bullish = close_price >= open_price
            candle_color = is_bullish ? :red : :green

            # 繪製影線（high-low line）
            plot!(p1, [date, date], [low_price, high_price],
                  color=candle_color, linewidth=1, label="", alpha=0.8)

            # 繪製實體（open-close rectangle）
            body_height = abs(close_price - open_price)
            body_bottom = min(open_price, close_price)
            body_top = max(open_price, close_price)

            if body_height > 0
                # 有實體的蠟燭
                plot!(p1, [date, date], [body_bottom, body_top],
                      color=candle_color, linewidth=4, label="", alpha=0.9)
            else
                # 十字星（開盤價等於收盤價）
                plot!(p1, [date, date], [open_price - 0.001, close_price + 0.001],
                      color=:black, linewidth=3, label="", alpha=0.9)
            end
        end
        
        # 創建成交量圖
        p2 = bar(dates, volumes, title="成交量",
                 color=:steelblue, alpha=0.6, label="Volume",
                 xlabel="日期", ylabel="成交量")

        # 組合圖表 - 主圖表佔3/4空間，成交量圖佔1/4空間
        combined_plot = plot(p1, p2, layout=@layout([a{0.75h}; b{0.25h}]), size=(1200, 800))
        
        return combined_plot
        
    catch e
        println("❌ 圖表繪製失敗：$e")
        println("回退到文字版圖表...")
        display_candlestick_data(ohlcv_data, title, min(20, max_candles))
        return nothing
    end
end

"""
繪製蠟燭圖+移動平均線（漲紅跌綠）
"""
function plot_candlestick_with_ma(ohlcv_data::Vector{OHLCVData}, title::String="蠟燭圖+移動平均",
                                 ma_periods::Vector{Int}=[5, 10, 20], max_candles::Int=100)
    if !PLOTS_AVAILABLE
        println("❌ Plots.jl 不可用，使用文字版圖表")
        display_candlestick_data(ohlcv_data, title, min(20, max_candles))
        return nothing
    end

    if isempty(ohlcv_data)
        throw(ArgumentError("數據不能為空"))
    end

    # 限制顯示的數據
    data_to_plot = if length(ohlcv_data) > max_candles
        ohlcv_data[end-max_candles+1:end]
    else
        ohlcv_data
    end

    dates = [d.date for d in data_to_plot]
    opens = [d.open for d in data_to_plot]
    highs = [d.high for d in data_to_plot]
    lows = [d.low for d in data_to_plot]
    closes = [d.close for d in data_to_plot]
    volumes = [d.volume for d in data_to_plot]

    try
        # 創建蠟燭圖 - 優化中文顯示
        font_settings = get_font_settings()
        p1 = plot(title=title,
                  xlabel="日期", ylabel="價格",
                  size=(1200, 400), grid=true, gridwidth=1, gridcolor=:lightgray,
                  background_color=:white,
                  titlefontsize=font_settings[:title],
                  guidefontsize=font_settings[:guide],
                  tickfontsize=font_settings[:tick],
                  legendfontsize=font_settings[:legend],
                  fontfamily=font_settings[:family])

        # 繪製蠟燭圖
        for i in 1:length(data_to_plot)
            date = dates[i]
            open_price = opens[i]
            high_price = highs[i]
            low_price = lows[i]
            close_price = closes[i]

            # 判斷漲跌：漲紅跌綠
            is_bullish = close_price >= open_price
            candle_color = is_bullish ? :red : :green

            # 繪製影線
            plot!(p1, [date, date], [low_price, high_price],
                  color=candle_color, linewidth=1, label="", alpha=0.7)

            # 繪製實體
            body_bottom = min(open_price, close_price)
            body_top = max(open_price, close_price)
            plot!(p1, [date, date], [body_bottom, body_top],
                  color=candle_color, linewidth=4, label="", alpha=0.8)
        end

        # 添加移動平均線
        ma_colors = [:blue, :orange, :purple, :brown, :pink]

        for (i, period) in enumerate(ma_periods)
            if length(closes) >= period
                ma_values = calculate_moving_average(closes, period)
                ma_dates = dates[period:end]

                color = ma_colors[min(i, length(ma_colors))]
                plot!(p1, ma_dates, ma_values,
                      color=color, linewidth=2,
                      label="MA$period", alpha=0.9)
            end
        end

        # 添加圖例說明
        plot!(p1, [], [], color=:red, linewidth=4, label="上漲", alpha=0.8)
        plot!(p1, [], [], color=:green, linewidth=4, label="下跌", alpha=0.8)

        # 創建成交量圖 - 優化中文顯示
        p2 = bar(dates, volumes, title="成交量",
                 color=:steelblue, alpha=0.6, label="成交量",
                 xlabel="日期", ylabel="成交量",
                 titlefontsize=font_settings[:guide],
                 guidefontsize=font_settings[:tick],
                 tickfontsize=font_settings[:tick]-1,
                 fontfamily=font_settings[:family])

        # 組合圖表 - 主圖表佔3/4空間，成交量圖佔1/4空間
        combined_plot = plot(p1, p2, layout=@layout([a{0.75h}; b{0.25h}]), size=(1200, 800))

        return combined_plot

    catch e
        println("❌ 圖表繪製失敗：$e")
        println("回退到文字版圖表...")
        display_candlestick_data(ohlcv_data, title, min(20, max_candles))
        return nothing
    end
end

"""
繪製帶移動平均線的價格圖（保持向後兼容）
"""
function plot_price_with_ma(ohlcv_data::Vector{OHLCVData}, title::String="價格+移動平均",
                           ma_periods::Vector{Int}=[5, 10, 20], max_candles::Int=100)
    if !PLOTS_AVAILABLE
        println("❌ Plots.jl 不可用，使用文字版圖表")
        display_candlestick_data(ohlcv_data, title, min(20, max_candles))
        return nothing
    end
    
    if isempty(ohlcv_data)
        throw(ArgumentError("數據不能為空"))
    end
    
    # 限制顯示的數據
    data_to_plot = if length(ohlcv_data) > max_candles
        ohlcv_data[end-max_candles+1:end]
    else
        ohlcv_data
    end
    
    dates = [d.date for d in data_to_plot]
    opens = [d.open for d in data_to_plot]
    highs = [d.high for d in data_to_plot]
    lows = [d.low for d in data_to_plot]
    closes = [d.close for d in data_to_plot]
    volumes = [d.volume for d in data_to_plot]

    try
        # 創建蠟燭圖
        p1 = plot(title=title,
                  xlabel="日期", ylabel="價格",
                  size=(1200, 400), grid=true, gridwidth=1, gridcolor=:lightgray)

        # 繪製蠟燭圖（簡化版，使用線條）
        for i in 1:length(data_to_plot)
            date = dates[i]
            open_price = opens[i]
            high_price = highs[i]
            low_price = lows[i]
            close_price = closes[i]

            # 判斷漲跌：漲紅跌綠
            is_bullish = close_price >= open_price
            candle_color = is_bullish ? :red : :green

            # 繪製影線
            plot!(p1, [date, date], [low_price, high_price],
                  color=candle_color, linewidth=1, label="", alpha=0.6)

            # 繪製實體
            body_bottom = min(open_price, close_price)
            body_top = max(open_price, close_price)
            plot!(p1, [date, date], [body_bottom, body_top],
                  color=candle_color, linewidth=3, label="", alpha=0.8)
        end

        # 添加移動平均線 - 使用更專業的顏色配置
        ma_colors = [RGB(0.0, 0.4, 0.8), RGB(0.8, 0.4, 0.0), RGB(0.6, 0.0, 0.6),
                     RGB(0.0, 0.6, 0.4), RGB(0.8, 0.0, 0.4)]

        for (i, period) in enumerate(ma_periods)
            if length(closes) >= period
                ma_values = calculate_moving_average(closes, period)
                ma_dates = dates[period:end]

                color = ma_colors[min(i, length(ma_colors))]
                plot!(p1, ma_dates, ma_values,
                      color=color, linewidth=2.0,
                      label="MA$period", alpha=0.9)
            end
        end
        
        # 創建成交量圖
        p2 = bar(dates, volumes, title="成交量",
                 color=:steelblue, alpha=0.6, label="Volume",
                 xlabel="日期", ylabel="成交量")

        # 組合圖表 - 主圖表佔3/4空間，成交量圖佔1/4空間
        combined_plot = plot(p1, p2, layout=@layout([a{0.75h}; b{0.25h}]), size=(1200, 800))
        
        return combined_plot
        
    catch e
        println("❌ 圖表繪製失敗：$e")
        println("回退到文字版圖表...")
        display_candlestick_data(ohlcv_data, title, min(20, max_candles))
        return nothing
    end
end

"""
計算移動平均線
"""
function calculate_moving_average(prices::Vector{Float64}, period::Int)::Vector{Float64}
    if length(prices) < period
        return Float64[]
    end
    
    ma_values = Float64[]
    
    for i in period:length(prices)
        window = prices[i-period+1:i]
        ma_value = mean(window)
        push!(ma_values, ma_value)
    end
    
    return ma_values
end

"""
繪製價格分佈直方圖
"""
function plot_price_histogram(ohlcv_data::Vector{OHLCVData}, title::String="價格分佈")
    if !PLOTS_AVAILABLE
        println("❌ Plots.jl 不可用，顯示統計信息")
        show_statistics(ohlcv_data, "數據", "分析")
        return nothing
    end
    
    if isempty(ohlcv_data)
        throw(ArgumentError("數據不能為空"))
    end
    
    closes = [d.close for d in ohlcv_data]
    
    try
        # 創建直方圖
        p = histogram(closes, bins=20, title=title,
                      xlabel="價格", ylabel="頻率",
                      color=:steelblue, alpha=0.7,
                      size=(800, 600))
        
        # 添加統計線
        mean_price = mean(closes)
        vline!(p, [mean_price], color=:red, linewidth=2, 
               label="平均值: $(round(mean_price, digits=4))")
        
        return p
        
    catch e
        println("❌ 直方圖繪製失敗：$e")
        show_statistics(ohlcv_data, "數據", "分析")
        return nothing
    end
end

"""
簡化版增強圖表顯示函數
"""
function show_simple_enhanced_chart(ticker::String="fan5_01", chart_type::String="weekly",
                                   chart_style::String="simple")
    println("📊 AmiBroker 簡化增強版圖表模擬器")
    println("="^50)

    # 設置中文字體支持
    setup_chinese_fonts()

    # 構建文件路徑
    file_path = if isfile(ticker)
        ticker  # 如果 ticker 本身就是一個有效的檔案路徑，則直接使用
    else
        # 否則，將其視為股票代碼並構建路徑
        "data_amibroker/fan5/g5/$(ticker).csv"
    end
    
    if !isfile(file_path)
        println("❌ 找不到數據文件：$file_path")
        println("\n💡 可能的原因：")
        println("   • 股票代碼輸入錯誤")
        println("   • 數據文件不存在")
        println("   • 文件路徑不正確")

        # 顯示可用的股票代碼
        available_tickers = get_available_tickers()
        if !isempty(available_tickers)
            println("\n📋 可用的股票代碼（前10個）：")
            for (i, t) in enumerate(available_tickers[1:min(10, length(available_tickers))])
                println("   $i. $t")
            end
            if length(available_tickers) > 10
                println("   ... 還有 $(length(available_tickers) - 10) 個")
            end
            println("\n💡 提示：請複製上述代碼之一重新嘗試")
        else
            println("\n❌ 未找到任何可用的股票數據")
            println("💡 請檢查 data_amibroker 目錄是否存在且包含數據文件")
        end
        return
    end
    
    try
        # 載入數據
        println("📈 載入數據：$ticker")
        daily_data = load_price_data(file_path)
        
        if isempty(daily_data)
            println("❌ 數據為空")
            return
        end
        
        println("✅ 載入 $(length(daily_data)) 天的數據")
        println("📅 數據範圍：$(daily_data[1].date) 到 $(daily_data[end].date)")
        
        # 根據圖表類型處理數據
        data_to_chart = if chart_type == "weekly"
            println("🔄 轉換為週線數據...")
            weekly_data = convert_to_weekly(daily_data)
            println("✅ 生成 $(length(weekly_data)) 週的數據")
            weekly_data
        else  # daily
            println("📊 使用日線數據...")
            daily_data
        end
        
        # 根據圖表樣式繪製
        chart_title = "$ticker $(chart_type == "weekly" ? "週線" : "日線")"

        println("\n🎯 開始生成圖表...")
        println("📊 數據點數：$(length(data_to_chart))")
        println("📅 時間範圍：$(data_to_chart[1].date) 至 $(data_to_chart[end].date)")

        chart = if chart_style == "candlestick"
            println("🕯️  繪製真正蠟燭圖（漲紅跌綠）...")
            println("💡 提示：紅色代表上漲，綠色代表下跌")
            plot_real_candlestick(data_to_chart, "$chart_title 蠟燭圖", 100)

        elseif chart_style == "candlestick_ma"
            println("🕯️  繪製蠟燭圖+移動平均線（漲紅跌綠）...")
            println("💡 提示：包含 MA5、MA10、MA20 移動平均線")
            plot_candlestick_with_ma(data_to_chart, "$chart_title 蠟燭圖+MA", [5, 10, 20], 100)

        elseif chart_style == "simple"
            println("🎨 繪製簡化專業圖表...")
            println("💡 提示：簡化版蠟燭圖，適合快速瀏覽")
            plot_simple_candlestick(data_to_chart, chart_title, 100)

        elseif chart_style == "ma"
            println("🎨 繪製價格+移動平均線圖表...")
            println("💡 提示：專注於趨勢分析")
            plot_price_with_ma(data_to_chart, chart_title, [5, 10, 20], 100)

        elseif chart_style == "histogram"
            println("🎨 繪製價格分佈直方圖...")
            println("💡 提示：顯示價格分佈統計")
            plot_price_histogram(data_to_chart, "$chart_title 價格分佈")

        elseif chart_style == "text"
            println("🎨 顯示文字版圖表...")
            println("💡 提示：適合在無圖形界面環境使用")
            display_candlestick_data(data_to_chart, chart_title, 20)
            plot_ascii_chart(data_to_chart, "$chart_title ASCII", 80, 15)
            return

        else
            println("❌ 未知的圖表樣式：$chart_style")
            println("💡 可用樣式：candlestick, candlestick_ma, simple, ma, histogram, text")
            return
        end
        
        # 顯示圖表
        if chart !== nothing && PLOTS_AVAILABLE
            println("✅ 圖表生成完成！")
            println("🖼️  正在顯示圖表...")
            display(chart)

            # 顯示圖表統計信息
            println("\n📈 圖表統計信息：")
            println("   • 最高價：$(maximum([d.high for d in data_to_chart]))")
            println("   • 最低價：$(minimum([d.low for d in data_to_chart]))")
            println("   • 期初價：$(data_to_chart[1].open)")
            println("   • 期末價：$(data_to_chart[end].close)")
            change_pct = round((data_to_chart[end].close - data_to_chart[1].open) / data_to_chart[1].open * 100, digits=2)
            println("   • 總變化：$(change_pct)%")

            # 保存選項
            print("\n💾 是否要保存圖表？(y/N): ")
            try
                save_choice = strip(readline())
                if lowercase(save_choice) == "y"
                    save_dir = "charts"
                    if !isdir(save_dir)
                        mkdir(save_dir)
                        println("📁 創建圖表保存目錄：$save_dir")
                    end

                    timestamp = Dates.format(now(), "yyyymmdd_HHMMSS")
                    filename = "$(ticker)_$(chart_type)_$(chart_style)_$(timestamp).html"
                    filepath = joinpath(save_dir, filename)
                    
                    savefig(chart, filepath)

                    # 修復HTML文件的中文顯示問題
                    fix_html_chinese_display(filepath)

                    println("✅ 圖表已保存：$filepath")
                    println("✅ HTML中文顯示已優化")
                end
            catch InterruptException
                println("\n⚠️  保存操作被取消")
            end
        end
        
        # 顯示統計信息
        println("\n📈 數據統計：")
        println("   • 數據點數：$(length(data_to_chart))")
        println("   • 時間範圍：$(data_to_chart[1].date) 至 $(data_to_chart[end].date)")
        if !isempty(data_to_chart)
            highs = [d.high for d in data_to_chart]
            lows = [d.low for d in data_to_chart]
            println("   • 最高價：$(maximum(highs))")
            println("   • 最低價：$(minimum(lows))")
            println("   • 期初價：$(data_to_chart[1].open)")
            println("   • 期末價：$(data_to_chart[end].close)")
            change_pct = round((data_to_chart[end].close - data_to_chart[1].open) / data_to_chart[1].open * 100, digits=2)
            println("   • 總變化：$(change_pct)%")
        end
        
    catch e
        println("❌ 處理數據時發生錯誤：$e")
        println("詳細錯誤信息：")
        showerror(stdout, e)
        println()
    end
end

"""
簡化版互動式選單
"""
function simple_enhanced_menu()
    while true
        println("\n📊 AmiBroker 簡化增強版圖表選單")
        println("-"^40)
        
        if PLOTS_AVAILABLE
            println("🕯️  真正蠟燭圖（漲紅跌綠）：")
            println("1. 真正蠟燭圖（週線）")
            println("2. 真正蠟燭圖（日線）")
            println("3. 蠟燭圖+移動平均線（週線）")
            println("4. 蠟燭圖+移動平均線（日線）")
            println()
            println("📈 其他專業圖表：")
            println("5. 簡化專業圖表（週線）")
            println("6. 簡化專業圖表（日線）")
            println("7. 價格分佈直方圖")
            println()
        else
            println("⚠️  Plots.jl 不可用，僅提供文字版功能")
        end

        println("📝 文字版選項：")
        println("8. 文字版蠟燭圖（週線）")
        println("9. 文字版蠟燭圖（日線）")
        println("10. ASCII 圖表")
        println()
        println("🔧 其他功能：")
        println("11. 查看可用股票列表")
        println("12. 退出")
        
        print("\n請選擇 (1-12): ")
        choice = strip(readline())

        if choice in ["1", "2", "3", "4", "5", "6", "7"] && PLOTS_AVAILABLE
            # 專業圖表選項
            print("請輸入股票代碼 (預設 fan5_01): ")
            ticker_input = strip(readline())
            ticker = isempty(ticker_input) ? "fan5_01" : String(ticker_input)

            chart_type = choice in ["1", "3", "5"] ? "weekly" : "daily"
            chart_style = if choice in ["1", "2"]
                "candlestick"  # 真正蠟燭圖
            elseif choice in ["3", "4"]
                "candlestick_ma"  # 蠟燭圖+移動平均線
            elseif choice in ["5", "6"]
                "simple"  # 簡化專業圖表
            elseif choice == "7"
                "histogram"
            end

            show_simple_enhanced_chart(ticker, chart_type, chart_style)
            
        elseif choice in ["8", "9", "10"]
            # 文字版圖表
            print("請輸入股票代碼 (預設 fan5_01): ")
            ticker_input = strip(readline())
            ticker = isempty(ticker_input) ? "fan5_01" : String(ticker_input)

            if choice == "8"
                show_simple_enhanced_chart(ticker, "weekly", "text")
            elseif choice == "9"
                show_simple_enhanced_chart(ticker, "daily", "text")
            elseif choice == "10"
                # 只顯示 ASCII 圖表
                file_path = "data_amibroker/fan5/g5/$(ticker).csv"
                if isfile(file_path)
                    try
                        daily_data = load_price_data(file_path)
                        print("選擇類型 (weekly/daily, 預設 weekly): ")
                        type_input = strip(readline())
                        chart_type = isempty(type_input) ? "weekly" : String(type_input)
                        
                        data_to_plot = chart_type == "weekly" ? convert_to_weekly(daily_data) : daily_data
                        plot_ascii_chart(data_to_plot, "$ticker $(chart_type) ASCII 圖表", 80, 20)
                    catch e
                        println("❌ ASCII 圖表生成失敗：$e")
                    end
                else
                    println("❌ 找不到數據文件：$file_path")
                end
            end
            
        elseif choice == "11"
            # 查看可用股票列表
            available_tickers = get_available_tickers()
            if !isempty(available_tickers)
                println("\n📋 可用股票列表 ($(length(available_tickers)) 個)：")
                for (i, ticker) in enumerate(available_tickers)
                    print("$(lpad(ticker, 10)) ")
                    if i % 6 == 0
                        println()
                    end
                end
                println()
            else
                println("❌ 找不到可用的股票數據")
            end

        elseif choice == "12"
            println("👋 再見！")
            break

        else
            if choice in ["1", "2", "3", "4", "5", "6", "7"] && !PLOTS_AVAILABLE
                println("❌ Plots.jl 不可用，請選擇文字版選項 (8-12)")
            else
                println("❌ 無效選擇，請輸入 1-12")
            end
        end
    end
end

# 主程序入口
if abspath(PROGRAM_FILE) == @__FILE__
    # 檢查是否有命令行參數
    if length(ARGS) >= 1
        ticker = ARGS[1]
        chart_type = length(ARGS) >= 2 ? ARGS[2] : "weekly"
        chart_style = length(ARGS) >= 3 ? ARGS[3] : "simple"
        show_simple_enhanced_chart(ticker, chart_type, chart_style)
    else
        simple_enhanced_menu()
    end
end
