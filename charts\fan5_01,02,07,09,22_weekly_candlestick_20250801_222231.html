<script src="https://cdn.plot.ly/plotly-2.6.3.min.js"></script>    <div id="1b0bbd81_f9fc_475e_960d_a14140c763c4" style="width:1200px;height:800px;"></div>
    <script>
    function plots_jl_plotly_1b0bbd81_f9fc_475e_960d_a14140c763c4() {
        
        Plotly.newPlot('1b0bbd81_f9fc_475e_960d_a14140c763c4', [
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738773.0,
            738773.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            8.77182094473555,
            9.87566267241481
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738773.0,
            738773.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            8.999999999999977,
            9.857142857142833
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738780.0,
            738780.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            7.846628853292097,
            9.08922656503134
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738780.0,
            738780.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            7.999999999999977,
            8.857142857142835
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738787.0,
            738787.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            6.759538257996018,
            7.865133158584545
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738787.0,
            738787.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            6.999999999999978,
            7.857142857142835
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738794.0,
            738794.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            5.866708590250867,
            7.021560265401103
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738794.0,
            738794.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            5.999999999999978,
            6.857142857142835
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738801.0,
            738801.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            5.69185469161074,
            6.817861501507364
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738801.0,
            738801.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            5.857142857142835,
            5.999999999999978
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738808.0,
            738808.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            4.896754627432073,
            5.93583511832887
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738808.0,
            738808.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            4.999999999999978,
            5.857142857142835
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738815.0,
            738815.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            4.258873054868165,
            5.250557997333292
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738815.0,
            738815.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            4.857142857142835,
            4.999999999999979
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738822.0,
            738822.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            4.131575732693013,
            5.34125517511585
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738822.0,
            738822.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            4.8571428571428354,
            4.999999999999979
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738829.0,
            738829.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            3.949003579124429,
            5.028613128420791
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738829.0,
            738829.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            3.999999999999979,
            4.857142857142836
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738836.0,
            738836.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            2.9893947938330667,
            3.920430065969772
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738836.0,
            738836.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            2.9999999999999796,
            3.8571428571428363
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738843.0,
            738843.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            1.921453812237764,
            2.8715533183233806
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738843.0,
            738843.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            1.99999999999998,
            2.857142857142837
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738850.0,
            738850.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            0.9956569720992225,
            1.8760560185121253
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738850.0,
            738850.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            0.9999999999999778,
            1.8571428571428372
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738857.0,
            738857.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            0.8530513346225665,
            1.7191680654352495
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738857.0,
            738857.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            0.8571428571428346,
            0.999999999999976
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738864.0,
            738864.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -2.6645352591003757e-14,
            0.877940712435541
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738864.0,
            738864.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -2.6645352591003757e-14,
            0.8571428571428328
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738871.0,
            738871.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -0.5714285714285996,
            0.2906210450134416
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738871.0,
            738871.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -0.14285714285716988,
            -2.7644553313166398e-14
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738878.0,
            738878.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -0.8571428571428845,
            -2.736699755701011e-14
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738878.0,
            738878.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -0.1428571428571705,
            -2.736699755701011e-14
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738885.0,
            738885.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -0.42857142857145597,
            0.4475282493852538
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738885.0,
            738885.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -0.14285714285717022,
            -2.731148640577885e-14
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738892.0,
            738892.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -1.000000000000027,
            -0.14285714285717016
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738892.0,
            738892.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -1.000000000000027,
            -0.14285714285717016
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738899.0,
            738899.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -1.2857142857143127,
            -0.4285714285714555
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738899.0,
            738899.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -1.1428571428571699,
            -1.0000000000000266
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738906.0,
            738906.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -1.0000000000000264,
            -0.1428571428571695
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738906.0,
            738906.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -1.0000000000000264,
            -0.1428571428571695
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738913.0,
            738913.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -1.285714285714312,
            -0.42857142857145486
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738913.0,
            738913.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -1.1428571428571692,
            -1.000000000000026
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738920.0,
            738920.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -1.5714285714285972,
            -0.71428571428574
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738920.0,
            738920.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -1.1428571428571688,
            -1.0000000000000255
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738927.0,
            738927.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -2.0000000000000253,
            -1.1428571428571683
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738927.0,
            738927.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -2.0000000000000253,
            -1.1428571428571683
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738934.0,
            738934.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -2.142857142857168,
            0.14425765106812236
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738934.0,
            738934.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -2.142857142857168,
            -2.4980018054066022e-14
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738941.0,
            738941.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -0.2857142857143107,
            0.581819087116645
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738941.0,
            738941.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -0.14285714285716783,
            -2.4868995751603507e-14
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738948.0,
            738948.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -0.28571428571431057,
            0.5771249691206112
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738948.0,
            738948.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -0.14285714285716772,
            -2.475797344914099e-14
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738955.0,
            738955.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -0.7142857142857387,
            0.14525954805067
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738955.0,
            738955.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -0.14285714285716755,
            -2.4313884239290928e-14
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738962.0,
            738962.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -0.2857142857143099,
            0.5905682709904426
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738962.0,
            738962.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -0.1428571428571671,
            -2.4091839634365897e-14
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738969.0,
            738969.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -0.2857142857143097,
            1.4349515435889921
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738969.0,
            738969.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -0.14285714285716689,
            0.9999999999999762
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738976.0,
            738976.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            0.5566732489571043,
            1.454201916726631
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738976.0,
            738976.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            0.8571428571428334,
            0.9999999999999765
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738983.0,
            738983.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -2.3425705819590803e-14,
            0.8639145629798303
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738983.0,
            738983.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -2.3425705819590803e-14,
            0.8571428571428337
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738990.0,
            738990.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -0.285714285714309,
            2.1692711547909718
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738990.0,
            738990.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -0.14285714285716622,
            1.999999999999977
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738997.0,
            738997.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            0.9995648647826882,
            1.866896348807247
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738997.0,
            738997.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            0.9999999999999774,
            1.8571428571428341
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739004.0,
            739004.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -2.2537527399890678e-14,
            0.8739423427193881
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739004.0,
            739004.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -2.2537527399890678e-14,
            0.8571428571428346
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739011.0,
            739011.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -0.5714285714285937,
            1.0129532539977915
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739011.0,
            739011.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -0.14285714285716533,
            0.9999999999999778
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739018.0,
            739018.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            0.561788565401634,
            1.465717089455162
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739018.0,
            739018.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            0.857142857142835,
            0.999999999999978
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739025.0,
            739025.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -2.1760371282653068e-14,
            0.8672607177297323
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739025.0,
            739025.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -2.1760371282653068e-14,
            0.8571428571428352
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739032.0,
            739032.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -1.0000000000000213,
            -0.14285714285716455
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739032.0,
            739032.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -1.0000000000000213,
            -0.14285714285716455
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739039.0,
            739039.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -2.0000000000000213,
            -1.1428571428571643
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739039.0,
            739039.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -2.0000000000000213,
            -1.1428571428571643
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739046.0,
            739046.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -2.285714285714307,
            -1.4285714285714495
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739046.0,
            739046.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -2.1428571428571637,
            -2.0000000000000204
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739053.0,
            739053.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -3.0000000000000204,
            -2.1428571428571637
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739053.0,
            739053.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -3.0000000000000204,
            -2.1428571428571637
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739060.0,
            739060.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -3.42857142857145,
            -2.5714285714285925
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739060.0,
            739060.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -3.1428571428571637,
            -3.0000000000000213
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739067.0,
            739067.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -3.428571428571451,
            -1.7142857142857364
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739067.0,
            739067.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -3.1428571428571646,
            -2.000000000000022
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739074.0,
            739074.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -3.0000000000000213,
            -2.1428571428571646
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739074.0,
            739074.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -3.0000000000000213,
            -2.1428571428571646
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739081.0,
            739081.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -3.285714285714308,
            -2.42857142857145
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739081.0,
            739081.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -3.1428571428571646,
            -3.0000000000000213
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739088.0,
            739088.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -3.0000000000000213,
            -2.1428571428571646
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739088.0,
            739088.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -3.0000000000000213,
            -2.1428571428571646
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739095.0,
            739095.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -4.000000000000023,
            -3.1428571428571646
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739095.0,
            739095.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -4.000000000000023,
            -3.1428571428571646
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739102.0,
            739102.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -4.142857142857165,
            -3.2857142857143087
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739102.0,
            739102.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -4.142857142857165,
            -4.000000000000023
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739109.0,
            739109.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -4.428571428571451,
            -2.85714285714288
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739109.0,
            739109.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -4.142857142857165,
            -3.000000000000022
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739116.0,
            739116.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -3.000000000000022,
            -2.1428571428571654
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739116.0,
            739116.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -3.000000000000022,
            -2.1428571428571654
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739123.0,
            739123.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -3.5714285714285934,
            -2.714285714285736
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739123.0,
            739123.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -3.142857142857165,
            -3.0000000000000218
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739130.0,
            739130.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -3.5714285714285934,
            -0.7142857142857357
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739130.0,
            739130.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -3.1428571428571646,
            -1.0000000000000213
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739137.0,
            739137.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -2.000000000000021,
            -1.142857142857164
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739137.0,
            739137.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -2.000000000000021,
            -1.142857142857164
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739144.0,
            739144.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -3.0000000000000204,
            -2.1428571428571637
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739144.0,
            739144.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -3.0000000000000204,
            -2.1428571428571637
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739151.0,
            739151.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -3.285714285714306,
            -2.428571428571449
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739151.0,
            739151.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -3.142857142857163,
            -3.00000000000002
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739158.0,
            739158.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -3.4285714285714484,
            -2.5714285714285916
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739158.0,
            739158.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -3.1428571428571628,
            -3.0000000000000195
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739165.0,
            739165.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -4.0000000000000195,
            -3.1428571428571628
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739165.0,
            739165.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -4.0000000000000195,
            -3.1428571428571628
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739172.0,
            739172.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -4.571428571428591,
            -3.7142857142857335
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739172.0,
            739172.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -4.142857142857163,
            -4.0000000000000195
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739179.0,
            739179.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -4.4285714285714475,
            -3.5714285714285907
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739179.0,
            739179.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -4.142857142857162,
            -4.0000000000000195
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739186.0,
            739186.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -4.571428571428591,
            -2.8571428571428763
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739186.0,
            739186.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -4.142857142857162,
            -3.000000000000019
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739193.0,
            739193.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -4.000000000000019,
            -3.142857142857162
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739193.0,
            739193.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -4.000000000000019,
            -3.142857142857162
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739200.0,
            739200.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -5.000000000000019,
            -4.142857142857162
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739200.0,
            739200.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -5.000000000000019,
            -4.142857142857162
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739207.0,
            739207.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -5.000000000000019,
            -4.142857142857162
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739207.0,
            739207.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -5.000000000000019,
            -4.142857142857162
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739214.0,
            739214.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -4.714285714285733,
            -3.8571428571428754
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739214.0,
            739214.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -4.142857142857161,
            -4.000000000000019
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739221.0,
            739221.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -5.000000000000018,
            -4.142857142857161
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739221.0,
            739221.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -5.000000000000018,
            -4.142857142857161
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739228.0,
            739228.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -5.142857142857161,
            -4.000000000000018
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739228.0,
            739228.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -5.142857142857161,
            -4.000000000000018
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739235.0,
            739235.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -4.142857142857161,
            -3.2857142857143034
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739235.0,
            739235.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -4.142857142857161,
            -4.000000000000018
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739242.0,
            739242.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -4.000000000000018,
            -3.1428571428571606
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739242.0,
            739242.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -4.000000000000018,
            -3.1428571428571606
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739249.0,
            739249.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -5.000000000000017,
            -4.14285714285716
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739249.0,
            739249.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -5.000000000000017,
            -4.14285714285716
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739256.0,
            739256.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -4.428571428571446,
            -3.5714285714285885
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739256.0,
            739256.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -4.14285714285716,
            -4.000000000000017
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739263.0,
            739263.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -5.000000000000017,
            -4.14285714285716
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739263.0,
            739263.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -5.000000000000017,
            -4.14285714285716
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739270.0,
            739270.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -6.000000000000016,
            -5.142857142857159
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739270.0,
            739270.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -6.000000000000016,
            -5.142857142857159
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739277.0,
            739277.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -7.000000000000016,
            -6.142857142857159
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739277.0,
            739277.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -7.000000000000016,
            -6.142857142857159
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739284.0,
            739284.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -8.000000000000016,
            -7.142857142857158
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739284.0,
            739284.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -8.000000000000016,
            -7.142857142857158
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739291.0,
            739291.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -9.000000000000018,
            -8.14285714285716
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739291.0,
            739291.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -9.000000000000018,
            -8.14285714285716
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739298.0,
            739298.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -10.000000000000021,
            -9.142857142857162
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739298.0,
            739298.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -10.000000000000021,
            -9.142857142857162
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739305.0,
            739305.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -11.000000000000025,
            -10.142857142857164
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739305.0,
            739305.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -11.000000000000025,
            -10.142857142857164
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739312.0,
            739312.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -9.000000000000025,
            -8.142857142857167
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739312.0,
            739312.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -9.000000000000025,
            -8.142857142857167
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739319.0,
            739319.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -10.000000000000027,
            -9.142857142857169
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739319.0,
            739319.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -10.000000000000027,
            -9.142857142857169
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739326.0,
            739326.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -10.285714285714313,
            -8.857142857142884
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739326.0,
            739326.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -10.142857142857169,
            -9.000000000000027
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739333.0,
            739333.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -10.000000000000027,
            -9.142857142857169
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739333.0,
            739333.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -10.000000000000027,
            -9.142857142857169
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739340.0,
            739340.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -9.285714285714311,
            -8.428571428571455
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739340.0,
            739340.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -9.142857142857169,
            -9.000000000000027
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739347.0,
            739347.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -10.000000000000027,
            -9.142857142857169
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739347.0,
            739347.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -10.000000000000027,
            -9.142857142857169
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739354.0,
            739354.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -11.000000000000025,
            -10.142857142857169
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739354.0,
            739354.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -11.000000000000025,
            -10.142857142857169
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739361.0,
            739361.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -12.000000000000025,
            -11.142857142857169
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739361.0,
            739361.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -12.000000000000025,
            -11.142857142857169
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739368.0,
            739368.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -13.000000000000025,
            -12.142857142857167
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739368.0,
            739368.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -13.000000000000025,
            -12.142857142857167
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739375.0,
            739375.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -13.142857142857167,
            -12.28571428571431
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739375.0,
            739375.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -13.142857142857167,
            -13.000000000000025
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739382.0,
            739382.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -14.000000000000025,
            -13.142857142857167
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739382.0,
            739382.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -14.000000000000025,
            -13.142857142857167
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739389.0,
            739389.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -14.142857142857169,
            -12.000000000000027
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739389.0,
            739389.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -14.142857142857169,
            -12.000000000000027
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739396.0,
            739396.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -13.000000000000027,
            -12.142857142857169
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739396.0,
            739396.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -13.000000000000027,
            -12.142857142857169
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739403.0,
            739403.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -13.000000000000027,
            -12.142857142857169
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739403.0,
            739403.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -13.000000000000027,
            -12.142857142857169
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739410.0,
            739410.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -13.142857142857169,
            -11.571428571428598
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739410.0,
            739410.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -13.142857142857169,
            -12.000000000000027
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739417.0,
            739417.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -13.000000000000025,
            -12.142857142857169
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739417.0,
            739417.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -13.000000000000025,
            -12.142857142857169
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739424.0,
            739424.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -14.000000000000025,
            -13.142857142857169
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739424.0,
            739424.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -14.000000000000025,
            -13.142857142857169
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739431.0,
            739431.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -15.000000000000025,
            -14.142857142857167
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739431.0,
            739431.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -15.000000000000025,
            -14.142857142857167
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739438.0,
            739438.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -16.000000000000025,
            -15.142857142857167
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739438.0,
            739438.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -16.000000000000025,
            -15.142857142857167
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739445.0,
            739445.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -16.857142857142883,
            -16.000000000000025
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739445.0,
            739445.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -16.142857142857167,
            -16.000000000000025
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739452.0,
            739452.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -16.285714285714313,
            -14.571428571428598
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739452.0,
            739452.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -16.142857142857167,
            -15.000000000000027
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739459.0,
            739459.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -16.000000000000025,
            -15.142857142857169
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739459.0,
            739459.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -16.000000000000025,
            -15.142857142857169
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739464.0,
            739464.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -16.714285714285744,
            -16.142857142857167
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739464.0,
            739464.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -16.714285714285744,
            -16.142857142857167
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738771.0,
            738771.0,
            738775.0,
            738775.0,
            738771.0,
            738771.0
        ],
        "showlegend": true,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738778.0,
            738778.0,
            738782.0,
            738782.0,
            738778.0,
            738778.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738785.0,
            738785.0,
            738789.0,
            738789.0,
            738785.0,
            738785.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738792.0,
            738792.0,
            738796.0,
            738796.0,
            738792.0,
            738792.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738799.0,
            738799.0,
            738803.0,
            738803.0,
            738799.0,
            738799.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738806.0,
            738806.0,
            738810.0,
            738810.0,
            738806.0,
            738806.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738813.0,
            738813.0,
            738817.0,
            738817.0,
            738813.0,
            738813.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738820.0,
            738820.0,
            738824.0,
            738824.0,
            738820.0,
            738820.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738827.0,
            738827.0,
            738831.0,
            738831.0,
            738827.0,
            738827.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738834.0,
            738834.0,
            738838.0,
            738838.0,
            738834.0,
            738834.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738841.0,
            738841.0,
            738845.0,
            738845.0,
            738841.0,
            738841.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738848.0,
            738848.0,
            738852.0,
            738852.0,
            738848.0,
            738848.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738855.0,
            738855.0,
            738859.0,
            738859.0,
            738855.0,
            738855.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738862.0,
            738862.0,
            738866.0,
            738866.0,
            738862.0,
            738862.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738869.0,
            738869.0,
            738873.0,
            738873.0,
            738869.0,
            738869.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738876.0,
            738876.0,
            738880.0,
            738880.0,
            738876.0,
            738876.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738883.0,
            738883.0,
            738887.0,
            738887.0,
            738883.0,
            738883.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738890.0,
            738890.0,
            738894.0,
            738894.0,
            738890.0,
            738890.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738897.0,
            738897.0,
            738901.0,
            738901.0,
            738897.0,
            738897.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738904.0,
            738904.0,
            738908.0,
            738908.0,
            738904.0,
            738904.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738911.0,
            738911.0,
            738915.0,
            738915.0,
            738911.0,
            738911.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738918.0,
            738918.0,
            738922.0,
            738922.0,
            738918.0,
            738918.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738925.0,
            738925.0,
            738929.0,
            738929.0,
            738925.0,
            738925.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738932.0,
            738932.0,
            738936.0,
            738936.0,
            738932.0,
            738932.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            3.0,
            0.0,
            0.0,
            3.0,
            3.0,
            3.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738939.0,
            738939.0,
            738943.0,
            738943.0,
            738939.0,
            738939.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738946.0,
            738946.0,
            738950.0,
            738950.0,
            738946.0,
            738946.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738953.0,
            738953.0,
            738957.0,
            738957.0,
            738953.0,
            738953.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738960.0,
            738960.0,
            738964.0,
            738964.0,
            738960.0,
            738960.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738967.0,
            738967.0,
            738971.0,
            738971.0,
            738967.0,
            738967.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            2.0,
            0.0,
            0.0,
            2.0,
            2.0,
            2.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738974.0,
            738974.0,
            738978.0,
            738978.0,
            738974.0,
            738974.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738981.0,
            738981.0,
            738985.0,
            738985.0,
            738981.0,
            738981.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738988.0,
            738988.0,
            738992.0,
            738992.0,
            738988.0,
            738988.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            3.0,
            0.0,
            0.0,
            3.0,
            3.0,
            3.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738995.0,
            738995.0,
            738999.0,
            738999.0,
            738995.0,
            738995.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739002.0,
            739002.0,
            739006.0,
            739006.0,
            739002.0,
            739002.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739009.0,
            739009.0,
            739013.0,
            739013.0,
            739009.0,
            739009.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            2.0,
            0.0,
            0.0,
            2.0,
            2.0,
            2.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739016.0,
            739016.0,
            739020.0,
            739020.0,
            739016.0,
            739016.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739023.0,
            739023.0,
            739027.0,
            739027.0,
            739023.0,
            739023.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739030.0,
            739030.0,
            739034.0,
            739034.0,
            739030.0,
            739030.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739037.0,
            739037.0,
            739041.0,
            739041.0,
            739037.0,
            739037.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739044.0,
            739044.0,
            739048.0,
            739048.0,
            739044.0,
            739044.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739051.0,
            739051.0,
            739055.0,
            739055.0,
            739051.0,
            739051.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739058.0,
            739058.0,
            739062.0,
            739062.0,
            739058.0,
            739058.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739065.0,
            739065.0,
            739069.0,
            739069.0,
            739065.0,
            739065.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            2.0,
            0.0,
            0.0,
            2.0,
            2.0,
            2.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739072.0,
            739072.0,
            739076.0,
            739076.0,
            739072.0,
            739072.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739079.0,
            739079.0,
            739083.0,
            739083.0,
            739079.0,
            739079.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739086.0,
            739086.0,
            739090.0,
            739090.0,
            739086.0,
            739086.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739093.0,
            739093.0,
            739097.0,
            739097.0,
            739093.0,
            739093.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739100.0,
            739100.0,
            739104.0,
            739104.0,
            739100.0,
            739100.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739107.0,
            739107.0,
            739111.0,
            739111.0,
            739107.0,
            739107.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            2.0,
            0.0,
            0.0,
            2.0,
            2.0,
            2.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739114.0,
            739114.0,
            739118.0,
            739118.0,
            739114.0,
            739114.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739121.0,
            739121.0,
            739125.0,
            739125.0,
            739121.0,
            739121.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739128.0,
            739128.0,
            739132.0,
            739132.0,
            739128.0,
            739128.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            3.0,
            0.0,
            0.0,
            3.0,
            3.0,
            3.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739135.0,
            739135.0,
            739139.0,
            739139.0,
            739135.0,
            739135.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739142.0,
            739142.0,
            739146.0,
            739146.0,
            739142.0,
            739142.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739149.0,
            739149.0,
            739153.0,
            739153.0,
            739149.0,
            739149.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739156.0,
            739156.0,
            739160.0,
            739160.0,
            739156.0,
            739156.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739163.0,
            739163.0,
            739167.0,
            739167.0,
            739163.0,
            739163.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739170.0,
            739170.0,
            739174.0,
            739174.0,
            739170.0,
            739170.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739177.0,
            739177.0,
            739181.0,
            739181.0,
            739177.0,
            739177.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739184.0,
            739184.0,
            739188.0,
            739188.0,
            739184.0,
            739184.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            2.0,
            0.0,
            0.0,
            2.0,
            2.0,
            2.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739191.0,
            739191.0,
            739195.0,
            739195.0,
            739191.0,
            739191.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739198.0,
            739198.0,
            739202.0,
            739202.0,
            739198.0,
            739198.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739205.0,
            739205.0,
            739209.0,
            739209.0,
            739205.0,
            739205.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739212.0,
            739212.0,
            739216.0,
            739216.0,
            739212.0,
            739212.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            2.0,
            0.0,
            0.0,
            2.0,
            2.0,
            2.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739219.0,
            739219.0,
            739223.0,
            739223.0,
            739219.0,
            739219.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739226.0,
            739226.0,
            739230.0,
            739230.0,
            739226.0,
            739226.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            2.0,
            0.0,
            0.0,
            2.0,
            2.0,
            2.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739233.0,
            739233.0,
            739237.0,
            739237.0,
            739233.0,
            739233.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739240.0,
            739240.0,
            739244.0,
            739244.0,
            739240.0,
            739240.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739247.0,
            739247.0,
            739251.0,
            739251.0,
            739247.0,
            739247.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739254.0,
            739254.0,
            739258.0,
            739258.0,
            739254.0,
            739254.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            2.0,
            0.0,
            0.0,
            2.0,
            2.0,
            2.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739261.0,
            739261.0,
            739265.0,
            739265.0,
            739261.0,
            739261.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739268.0,
            739268.0,
            739272.0,
            739272.0,
            739268.0,
            739268.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739275.0,
            739275.0,
            739279.0,
            739279.0,
            739275.0,
            739275.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739282.0,
            739282.0,
            739286.0,
            739286.0,
            739282.0,
            739282.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739289.0,
            739289.0,
            739293.0,
            739293.0,
            739289.0,
            739289.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739296.0,
            739296.0,
            739300.0,
            739300.0,
            739296.0,
            739296.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739303.0,
            739303.0,
            739307.0,
            739307.0,
            739303.0,
            739303.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739310.0,
            739310.0,
            739314.0,
            739314.0,
            739310.0,
            739310.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            3.0,
            0.0,
            0.0,
            3.0,
            3.0,
            3.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739317.0,
            739317.0,
            739321.0,
            739321.0,
            739317.0,
            739317.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739324.0,
            739324.0,
            739328.0,
            739328.0,
            739324.0,
            739324.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            2.0,
            0.0,
            0.0,
            2.0,
            2.0,
            2.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739331.0,
            739331.0,
            739335.0,
            739335.0,
            739331.0,
            739331.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739338.0,
            739338.0,
            739342.0,
            739342.0,
            739338.0,
            739338.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            2.0,
            0.0,
            0.0,
            2.0,
            2.0,
            2.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739345.0,
            739345.0,
            739349.0,
            739349.0,
            739345.0,
            739345.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739352.0,
            739352.0,
            739356.0,
            739356.0,
            739352.0,
            739352.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739359.0,
            739359.0,
            739363.0,
            739363.0,
            739359.0,
            739359.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739366.0,
            739366.0,
            739370.0,
            739370.0,
            739366.0,
            739366.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739373.0,
            739373.0,
            739377.0,
            739377.0,
            739373.0,
            739373.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739380.0,
            739380.0,
            739384.0,
            739384.0,
            739380.0,
            739380.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739387.0,
            739387.0,
            739391.0,
            739391.0,
            739387.0,
            739387.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            3.0,
            0.0,
            0.0,
            3.0,
            3.0,
            3.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739394.0,
            739394.0,
            739398.0,
            739398.0,
            739394.0,
            739394.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739401.0,
            739401.0,
            739405.0,
            739405.0,
            739401.0,
            739401.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739408.0,
            739408.0,
            739412.0,
            739412.0,
            739408.0,
            739408.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            2.0,
            0.0,
            0.0,
            2.0,
            2.0,
            2.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739415.0,
            739415.0,
            739419.0,
            739419.0,
            739415.0,
            739415.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739422.0,
            739422.0,
            739426.0,
            739426.0,
            739422.0,
            739422.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739429.0,
            739429.0,
            739433.0,
            739433.0,
            739429.0,
            739429.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739436.0,
            739436.0,
            739440.0,
            739440.0,
            739436.0,
            739436.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739443.0,
            739443.0,
            739447.0,
            739447.0,
            739443.0,
            739443.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739450.0,
            739450.0,
            739454.0,
            739454.0,
            739450.0,
            739450.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            2.0,
            0.0,
            0.0,
            2.0,
            2.0,
            2.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739457.0,
            739457.0,
            739461.0,
            739461.0,
            739457.0,
            739457.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739462.0,
            739462.0,
            739466.0,
            739466.0,
            739462.0,
            739462.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "colorbar": {
            "y": 0.14547278269903766,
            "title": "",
            "len": 0.21513082349081364,
            "x": 0.996719160104987
        },
        "yaxis": "y2",
        "x": [
            738773.0,
            738780.0,
            738787.0,
            738794.0,
            738801.0,
            738808.0,
            738815.0,
            738822.0,
            738829.0,
            738836.0,
            738843.0,
            738850.0,
            738857.0,
            738864.0,
            738871.0,
            738878.0,
            738885.0,
            738892.0,
            738899.0,
            738906.0,
            738913.0,
            738920.0,
            738927.0,
            738934.0,
            738941.0,
            738948.0,
            738955.0,
            738962.0,
            738969.0,
            738976.0,
            738983.0,
            738990.0,
            738997.0,
            739004.0,
            739011.0,
            739018.0,
            739025.0,
            739032.0,
            739039.0,
            739046.0,
            739053.0,
            739060.0,
            739067.0,
            739074.0,
            739081.0,
            739088.0,
            739095.0,
            739102.0,
            739109.0,
            739116.0,
            739123.0,
            739130.0,
            739137.0,
            739144.0,
            739151.0,
            739158.0,
            739165.0,
            739172.0,
            739179.0,
            739186.0,
            739193.0,
            739200.0,
            739207.0,
            739214.0,
            739221.0,
            739228.0,
            739235.0,
            739242.0,
            739249.0,
            739256.0,
            739263.0,
            739270.0,
            739277.0,
            739284.0,
            739291.0,
            739298.0,
            739305.0,
            739312.0,
            739319.0,
            739326.0,
            739333.0,
            739340.0,
            739347.0,
            739354.0,
            739361.0,
            739368.0,
            739375.0,
            739382.0,
            739389.0,
            739396.0,
            739403.0,
            739410.0,
            739417.0,
            739424.0,
            739431.0,
            739438.0,
            739445.0,
            739452.0,
            739459.0,
            739464.0
        ],
        "showlegend": false,
        "mode": "markers",
        "name": "成交量",
        "legendgroup": "成交量",
        "marker": {
            "symbol": "circle",
            "color": "rgba(51, 102, 178, 0.000)",
            "line": {
                "color": "rgba(0, 0, 0, 0.000)",
                "width": 1
            },
            "size": 0
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            1.0,
            0.0,
            1.0,
            1.0,
            0.0,
            0.0,
            0.0,
            0.0,
            1.0,
            0.0,
            1.0,
            1.0,
            1.0,
            0.0,
            1.0,
            1.0,
            1.0,
            1.0,
            0.0,
            3.0,
            1.0,
            1.0,
            1.0,
            1.0,
            2.0,
            1.0,
            0.0,
            3.0,
            0.0,
            0.0,
            2.0,
            1.0,
            0.0,
            0.0,
            0.0,
            1.0,
            0.0,
            1.0,
            2.0,
            0.0,
            1.0,
            1.0,
            0.0,
            1.0,
            2.0,
            1.0,
            1.0,
            3.0,
            0.0,
            0.0,
            1.0,
            1.0,
            0.0,
            1.0,
            1.0,
            2.0,
            0.0,
            0.0,
            1.0,
            2.0,
            0.0,
            2.0,
            1.0,
            1.0,
            0.0,
            2.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            3.0,
            0.0,
            2.0,
            0.0,
            2.0,
            0.0,
            0.0,
            0.0,
            0.0,
            1.0,
            0.0,
            3.0,
            0.0,
            1.0,
            2.0,
            0.0,
            0.0,
            0.0,
            0.0,
            1.0,
            2.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    }
]
, {
    "showlegend": true,
    "paper_bgcolor": "rgba(255, 255, 255, 1.000)",
    "xaxis1": {
        "showticklabels": true,
        "gridwidth": 1,
        "tickvals": [
            738886.0,
            739252.0
        ],
        "range": [
            738752.27,
            739484.73
        ],
        "domain": [
            0.04494750656167979,
            0.996719160104987
        ],
        "mirror": false,
        "tickangle": 0,
        "showline": true,
        "ticktext": [
            "2024-01-01",
            "2025-01-01"
        ],
        "zeroline": false,
        "tickfont": {
            "color": "rgba(0, 0, 0, 1.000)",
            "family": "Microsoft YaHei, SimHei, Arial Unicode MS, sans-serif",
            "size": 14
        },
        "zerolinecolor": "rgba(0, 0, 0, 1.000)",
        "anchor": "y1",
        "visible": true,
        "ticks": "inside",
        "tickmode": "array",
        "linecolor": "rgba(0, 0, 0, 1.000)",
        "showgrid": true,
        "title": "日期",
        "gridcolor": "rgba(211, 211, 211, 0.300)",
        "titlefont": {
            "color": "rgba(0, 0, 0, 1.000)",
            "family": "Microsoft YaHei, SimHei, Arial Unicode MS, sans-serif",
            "size": 17
        },
        "tickcolor": "rgb(0, 0, 0)",
        "type": "-"
    },
    "legend": {
        "yanchor": "auto",
        "xanchor": "auto",
        "bordercolor": "rgba(0, 0, 0, 1.000)",
        "bgcolor": "rgba(255, 255, 255, 1.000)",
        "borderwidth": 1,
        "tracegroupgap": 0,
        "y": 1.0,
        "font": {
            "color": "rgba(0, 0, 0, 1.000)",
            "family": "Microsoft YaHei, SimHei, Arial Unicode MS, sans-serif",
            "size": 15
        },
        "title": {
            "font": {
                "color": "rgba(0, 0, 0, 1.000)",
                "family": "Microsoft YaHei, SimHei, Arial Unicode MS, sans-serif",
                "size": 15
            },
            "text": ""
        },
        "traceorder": "normal",
        "x": 1.0
    },
    "height": 800,
    "yaxis2": {
        "showticklabels": true,
        "gridwidth": 1,
        "tickvals": [
            0.0,
            1.0,
            2.0,
            3.0
        ],
        "range": [
            0.0,
            3.0
        ],
        "domain": [
            0.03790737095363082,
            0.2530381944444445
        ],
        "mirror": false,
        "tickangle": 0,
        "showline": true,
        "ticktext": [
            "0",
            "1",
            "2",
            "3"
        ],
        "zeroline": false,
        "tickfont": {
            "color": "rgba(0, 0, 0, 1.000)",
            "family": "Microsoft YaHei, SimHei, Arial Unicode MS, sans-serif",
            "size": 13
        },
        "zerolinecolor": "rgba(0, 0, 0, 1.000)",
        "anchor": "x2",
        "visible": true,
        "ticks": "inside",
        "tickmode": "array",
        "linecolor": "rgba(0, 0, 0, 1.000)",
        "showgrid": true,
        "title": "成交量",
        "gridcolor": "rgba(211, 211, 211, 0.300)",
        "titlefont": {
            "color": "rgba(0, 0, 0, 1.000)",
            "family": "Microsoft YaHei, SimHei, Arial Unicode MS, sans-serif",
            "size": 14
        },
        "tickcolor": "rgb(0, 0, 0)",
        "type": "-"
    },
    "xaxis2": {
        "showticklabels": true,
        "gridwidth": 1,
        "tickvals": [
            738886.0,
            739252.0
        ],
        "range": [
            738728.049,
            739508.951
        ],
        "domain": [
            0.04494750656167979,
            0.996719160104987
        ],
        "mirror": false,
        "tickangle": 0,
        "showline": true,
        "ticktext": [
            "2024-01-01",
            "2025-01-01"
        ],
        "zeroline": false,
        "tickfont": {
            "color": "rgba(0, 0, 0, 1.000)",
            "family": "Microsoft YaHei, SimHei, Arial Unicode MS, sans-serif",
            "size": 13
        },
        "zerolinecolor": "rgba(0, 0, 0, 1.000)",
        "anchor": "y2",
        "visible": true,
        "ticks": "inside",
        "tickmode": "array",
        "linecolor": "rgba(0, 0, 0, 1.000)",
        "showgrid": true,
        "title": "日期",
        "gridcolor": "rgba(211, 211, 211, 0.300)",
        "titlefont": {
            "color": "rgba(0, 0, 0, 1.000)",
            "family": "Microsoft YaHei, SimHei, Arial Unicode MS, sans-serif",
            "size": 14
        },
        "tickcolor": "rgb(0, 0, 0)",
        "type": "-"
    },
    "yaxis1": {
        "showticklabels": true,
        "gridwidth": 1,
        "tickvals": [
            -15.0,
            -10.0,
            -5.0,
            0.0,
            5.0,
            10.0
        ],
        "range": [
            -17.659127023029615,
            10.677646838301541
        ],
        "domain": [
            0.3219084919072616,
            0.9673009623797024
        ],
        "mirror": false,
        "tickangle": 0,
        "showline": true,
        "ticktext": [
            "-15",
            "-10",
            "-5",
            "0",
            "5",
            "10"
        ],
        "zeroline": false,
        "tickfont": {
            "color": "rgba(0, 0, 0, 1.000)",
            "family": "Microsoft YaHei, SimHei, Arial Unicode MS, sans-serif",
            "size": 14
        },
        "zerolinecolor": "rgba(0, 0, 0, 1.000)",
        "anchor": "x1",
        "visible": true,
        "ticks": "inside",
        "tickmode": "array",
        "linecolor": "rgba(0, 0, 0, 1.000)",
        "showgrid": true,
        "title": "價格",
        "gridcolor": "rgba(211, 211, 211, 0.300)",
        "titlefont": {
            "color": "rgba(0, 0, 0, 1.000)",
            "family": "Microsoft YaHei, SimHei, Arial Unicode MS, sans-serif",
            "size": 17
        },
        "tickcolor": "rgb(0, 0, 0)",
        "type": "-"
    },
    "annotations": [
        {
            "yanchor": "top",
            "xanchor": "center",
            "rotation": -0.0,
            "y": 1.0,
            "font": {
                "color": "rgba(0, 0, 0, 1.000)",
                "family": "Microsoft YaHei, SimHei, Arial Unicode MS, sans-serif",
                "size": 22
            },
            "yref": "paper",
            "showarrow": false,
            "text": "fan5_01,02,07,09,22 週線 蠟燭圖",
            "xref": "paper",
            "x": 0.5208333333333333
        },
        {
            "yanchor": "top",
            "xanchor": "center",
            "rotation": -0.0,
            "y": 0.27879278762029747,
            "font": {
                "color": "rgba(0, 0, 0, 1.000)",
                "family": "Microsoft YaHei, SimHei, Arial Unicode MS, sans-serif",
                "size": 17
            },
            "yref": "paper",
            "showarrow": false,
            "text": "成交量",
            "xref": "paper",
            "x": 0.5208333333333333
        }
    ],
    "plot_bgcolor": "rgba(255, 255, 255, 1.000)",
    "margin": {
        "l": 0,
        "b": 20,
        "r": 0,
        "t": 20
    },
    "width": 1200
}
);
        
    }
    </script>
    <script src="https://requirejs.org/docs/release/2.3.7/minified/require.js" onload="plots_jl_plotly_1b0bbd81_f9fc_475e_960d_a14140c763c4()"></script>
