#!/usr/bin/env julia

"""
AmiBroker 圖表模擬器 - 增強版
使用 Plots.jl 繪製專業蠟燭圖
包含原有的文字版功能 + 真實圖表功能
"""

using CSV
using DataFrames
using Dates
using Statistics
using Printf
using Plots
using PlotlyJS
using StatsPlots

# 設定繪圖後端
Plots.plotlyjs()

# 包含原有的基本功能
include("amibroker_chart_simulator.jl")

"""
使用 Plots.jl 繪製專業蠟燭圖
"""
function plot_professional_candlestick(ohlcv_data::Vector{OHLCVData}, title::String="專業蠟燭圖", max_candles::Int=100)
    if isempty(ohlcv_data)
        throw(ArgumentError("數據不能為空"))
    end
    
    # 限制顯示的蠟燭數量以提高性能
    data_to_plot = if length(ohlcv_data) > max_candles
        ohlcv_data[end-max_candles+1:end]
    else
        ohlcv_data
    end
    
    # 提取數據
    dates = [d.date for d in data_to_plot]
    opens = [d.open for d in data_to_plot]
    highs = [d.high for d in data_to_plot]
    lows = [d.low for d in data_to_plot]
    closes = [d.close for d in data_to_plot]
    volumes = [d.volume for d in data_to_plot]
    
    # 創建主圖表（價格）
    p1 = Plots.plot(title=title, size=(1200, 600),
              xlabel="日期", ylabel="價格",
              grid=true, gridwidth=1, gridcolor=:lightgray)
    
    # 繪製蠟燭圖
    for i in 1:length(data_to_plot)
        date = dates[i]
        open_price = opens[i]
        high_price = highs[i]
        low_price = lows[i]
        close_price = closes[i]
        
        # 決定顏色：綠色（上漲）或紅色（下跌）
        is_bullish = close_price >= open_price
        candle_color = is_bullish ? :green : :red
        
        # 繪製影線（high-low line）
        Plots.plot!(p1, [date, date], [low_price, high_price],
              color=candle_color, linewidth=1, label="", alpha=0.8)

        # 繪製實體（open-close rectangle）
        body_height = abs(close_price - open_price)
        body_bottom = min(open_price, close_price)
        body_top = max(open_price, close_price)

        if body_height > 0
            # 有實體的蠟燭
            Plots.plot!(p1, [date, date], [body_bottom, body_top],
                  color=candle_color, linewidth=4, label="", alpha=0.9)
        else
            # 十字星（開盤價等於收盤價）
            Plots.plot!(p1, [date, date], [open_price - 0.001, close_price + 0.001],
                  color=:black, linewidth=3, label="", alpha=0.9)
        end
    end
    
    # 創建成交量子圖
    p2 = Plots.bar(dates, volumes, title="成交量",
             color=:steelblue, alpha=0.6, label="Volume",
             xlabel="日期", ylabel="成交量",
             size=(1200, 200))

    # 組合圖表
    combined_plot = Plots.plot(p1, p2, layout=(2,1), size=(1200, 800),
                        plot_title="$title - 專業蠟燭圖")
    
    return combined_plot
end

"""
繪製帶移動平均線的蠟燭圖
"""
function plot_candlestick_with_ma(ohlcv_data::Vector{OHLCVData}, title::String="蠟燭圖+移動平均", 
                                  ma_periods::Vector{Int}=[5, 10, 20], max_candles::Int=100)
    if isempty(ohlcv_data)
        throw(ArgumentError("數據不能為空"))
    end
    
    # 限制顯示的蠟燭數量
    data_to_plot = if length(ohlcv_data) > max_candles
        ohlcv_data[end-max_candles+1:end]
    else
        ohlcv_data
    end
    
    # 提取數據
    dates = [d.date for d in data_to_plot]
    opens = [d.open for d in data_to_plot]
    highs = [d.high for d in data_to_plot]
    lows = [d.low for d in data_to_plot]
    closes = [d.close for d in data_to_plot]
    volumes = [d.volume for d in data_to_plot]
    
    # 創建主圖表
    p1 = plot(title=title, size=(1200, 600),
              xlabel="日期", ylabel="價格",
              grid=true, gridwidth=1, gridcolor=:lightgray)
    
    # 繪製蠟燭圖（簡化版，使用線條）
    for i in 1:length(data_to_plot)
        date = dates[i]
        open_price = opens[i]
        high_price = highs[i]
        low_price = lows[i]
        close_price = closes[i]
        
        # 決定顏色
        is_bullish = close_price >= open_price
        candle_color = is_bullish ? :green : :red
        
        # 繪製影線
        plot!(p1, [date, date], [low_price, high_price], 
              color=candle_color, linewidth=1, label="", alpha=0.6)
        
        # 繪製實體
        body_bottom = min(open_price, close_price)
        body_top = max(open_price, close_price)
        plot!(p1, [date, date], [body_bottom, body_top],
              color=candle_color, linewidth=3, label="", alpha=0.8)
    end
    
    # 計算並繪製移動平均線
    ma_colors = [:blue, :orange, :purple, :brown, :pink]
    
    for (i, period) in enumerate(ma_periods)
        if length(closes) >= period
            ma_values = calculate_moving_average(closes, period)
            ma_dates = dates[period:end]
            
            color = ma_colors[min(i, length(ma_colors))]
            plot!(p1, ma_dates, ma_values, 
                  color=color, linewidth=2, 
                  label="MA$period", alpha=0.8)
        end
    end
    
    # 創建成交量子圖
    p2 = bar(dates, volumes, title="成交量",
             color=:steelblue, alpha=0.6, label="Volume",
             xlabel="日期", ylabel="成交量")

    # 組合圖表 - 主圖表佔3/4空間，成交量圖佔1/4空間
    combined_plot = plot(p1, p2, layout=@layout([a{0.75h}; b{0.25h}]), size=(1200, 900),
                        plot_title="$title - 蠟燭圖與移動平均線")
    
    return combined_plot
end

"""
計算移動平均線
"""
function calculate_moving_average(prices::Vector{Float64}, period::Int)::Vector{Float64}
    if length(prices) < period
        return Float64[]
    end
    
    ma_values = Float64[]
    
    for i in period:length(prices)
        window = prices[i-period+1:i]
        ma_value = mean(window)
        push!(ma_values, ma_value)
    end
    
    return ma_values
end

"""
繪製價格分佈直方圖
"""
function plot_price_distribution(ohlcv_data::Vector{OHLCVData}, title::String="價格分佈")
    if isempty(ohlcv_data)
        throw(ArgumentError("數據不能為空"))
    end
    
    closes = [d.close for d in ohlcv_data]
    
    # 創建直方圖
    p = histogram(closes, bins=30, title=title,
                  xlabel="價格", ylabel="頻率",
                  color=:steelblue, alpha=0.7,
                  size=(800, 600))
    
    # 添加統計線
    mean_price = mean(closes)
    median_price = median(closes)
    
    vline!(p, [mean_price], color=:red, linewidth=2, 
           label="平均值: $(round(mean_price, digits=4))")
    vline!(p, [median_price], color=:green, linewidth=2, 
           label="中位數: $(round(median_price, digits=4))")
    
    return p
end

"""
繪製價格與成交量散點圖
"""
function plot_price_volume_scatter(ohlcv_data::Vector{OHLCVData}, title::String="價格vs成交量")
    if isempty(ohlcv_data)
        throw(ArgumentError("數據不能為空"))
    end
    
    closes = [d.close for d in ohlcv_data]
    volumes = [d.volume for d in ohlcv_data]
    
    # 創建散點圖
    p = scatter(volumes, closes, title=title,
                xlabel="成交量", ylabel="價格",
                color=:steelblue, alpha=0.6,
                size=(800, 600), markersize=3)
    
    # 添加趨勢線（如果有足夠的數據點）
    if length(closes) > 10
        # 簡單線性回歸
        x_mean = mean(volumes)
        y_mean = mean(closes)
        
        numerator = sum((volumes[i] - x_mean) * (closes[i] - y_mean) for i in 1:length(volumes))
        denominator = sum((volumes[i] - x_mean)^2 for i in 1:length(volumes))
        
        if denominator != 0
            slope = numerator / denominator
            intercept = y_mean - slope * x_mean
            
            x_range = [minimum(volumes), maximum(volumes)]
            y_range = [intercept + slope * x for x in x_range]
            
            plot!(p, x_range, y_range, color=:red, linewidth=2, 
                  label="趨勢線", alpha=0.8)
        end
    end
    
    return p
end

"""
增強版圖表顯示函數
"""
function show_enhanced_chart(ticker::String="fan5_01", chart_type::String="weekly", 
                           chart_style::String="professional")
    println("📊 AmiBroker 增強版圖表模擬器")
    println("="^50)
    
    # 構建文件路徑
    file_path = "data_amibroker/fan5/g1/$(ticker).csv"
    
    if !isfile(file_path)
        println("❌ 找不到數據文件：$file_path")
        
        # 顯示可用的股票代碼
        available_tickers = get_available_tickers()
        if !isempty(available_tickers)
            println("\n📋 可用的股票代碼：")
            for (i, t) in enumerate(available_tickers[1:min(10, length(available_tickers))])
                println("  $i. $t")
            end
            if length(available_tickers) > 10
                println("  ... 還有 $(length(available_tickers) - 10) 個")
            end
        end
        return
    end
    
    try
        # 載入數據
        println("📈 載入數據：$ticker")
        daily_data = load_price_data(file_path)
        
        if isempty(daily_data)
            println("❌ 數據為空")
            return
        end
        
        println("✅ 載入 $(length(daily_data)) 天的數據")
        println("📅 數據範圍：$(daily_data[1].date) 到 $(daily_data[end].date)")
        
        # 根據圖表類型處理數據
        data_to_chart = if chart_type == "weekly"
            println("🔄 轉換為週線數據...")
            weekly_data = convert_to_weekly(daily_data)
            println("✅ 生成 $(length(weekly_data)) 週的數據")
            weekly_data
        else  # daily
            println("📊 使用日線數據...")
            daily_data
        end
        
        # 根據圖表樣式繪製
        chart_title = "$ticker $(chart_type == "weekly" ? "週線" : "日線")"
        
        chart = if chart_style == "professional"
            println("🎨 繪製專業蠟燭圖...")
            plot_professional_candlestick(data_to_chart, chart_title, 100)
            
        elseif chart_style == "ma"
            println("🎨 繪製帶移動平均線的蠟燭圖...")
            plot_candlestick_with_ma(data_to_chart, chart_title, [5, 10, 20], 100)
            
        elseif chart_style == "distribution"
            println("🎨 繪製價格分佈圖...")
            plot_price_distribution(data_to_chart, "$chart_title 價格分佈")
            
        elseif chart_style == "scatter"
            println("🎨 繪製價格成交量散點圖...")
            plot_price_volume_scatter(data_to_chart, "$chart_title 價格vs成交量")
            
        else
            println("❌ 未知的圖表樣式：$chart_style")
            return
        end
        
        # 顯示圖表
        println("✅ 圖表生成完成，正在顯示...")
        display(chart)
        
        # 顯示統計信息
        show_statistics(data_to_chart, ticker, chart_type)
        
        # 保存圖表（可選）
        save_chart_option(chart, ticker, chart_type, chart_style)
        
    catch e
        println("❌ 處理數據時發生錯誤：$e")
        println("詳細錯誤信息：")
        showerror(stdout, e)
        println()
    end
end

"""
保存圖表選項
"""
function save_chart_option(chart, ticker::String, chart_type::String, chart_style::String)
    print("\n💾 是否要保存圖表？(y/N): ")

    try
        save_choice = strip(readline())
        if lowercase(save_choice) == "y"
            # 創建保存目錄
            save_dir = "charts"
            if !isdir(save_dir)
                mkdir(save_dir)
            end

            # 生成文件名
            timestamp = Dates.format(now(), "yyyymmdd_HHMMSS")
            filename = "$(ticker)_$(chart_type)_$(chart_style)_$(timestamp).html"
            filepath = joinpath(save_dir, filename)

            # 保存圖表
            savefig(chart, filepath)
            println("✅ 圖表已保存：$filepath")
        end
    catch InterruptException
        println("\n⚠️  保存操作被取消")
    end
end

"""
增強版互動式選單
"""
function enhanced_interactive_menu()
    while true
        println("\n📊 AmiBroker 增強版圖表模擬器選單")
        println("-"^45)
        println("🎨 專業圖表選項：")
        println("1. 專業蠟燭圖（週線）")
        println("2. 專業蠟燭圖（日線）")
        println("3. 蠟燭圖 + 移動平均線（週線）")
        println("4. 蠟燭圖 + 移動平均線（日線）")
        println("5. 價格分佈直方圖")
        println("6. 價格vs成交量散點圖")
        println()
        println("📝 文字版選項：")
        println("7. 文字版蠟燭圖（週線）")
        println("8. 文字版蠟燭圖（日線）")
        println("9. ASCII 圖表")
        println()
        println("🔧 其他功能：")
        println("10. 查看可用股票列表")
        println("11. 批量生成圖表")
        println("12. 圖表比較模式")
        println("13. 退出")

        print("\n請選擇 (1-13): ")
        choice = strip(readline())

        if choice in ["1", "2", "3", "4", "5", "6"]
            # 專業圖表選項
            print("請輸入股票代碼 (預設 fan5_01): ")
            ticker_input = strip(readline())
            ticker = isempty(ticker_input) ? "fan5_01" : String(ticker_input)

            chart_type = choice in ["1", "3"] ? "weekly" : choice in ["2", "4"] ? "daily" : "daily"
            chart_style = if choice in ["1", "2"]
                "professional"
            elseif choice in ["3", "4"]
                "ma"
            elseif choice == "5"
                "distribution"
            elseif choice == "6"
                "scatter"
            end

            show_enhanced_chart(ticker, chart_type, chart_style)

        elseif choice in ["7", "8"]
            # 文字版圖表
            print("請輸入股票代碼 (預設 fan5_01): ")
            ticker_input = strip(readline())
            ticker = isempty(ticker_input) ? "fan5_01" : String(ticker_input)

            chart_type = choice == "7" ? "weekly" : "daily"
            show_chart(ticker, chart_type)  # 使用原有的文字版功能

        elseif choice == "9"
            # ASCII 圖表
            print("請輸入股票代碼 (預設 fan5_01): ")
            ticker_input = strip(readline())
            ticker = isempty(ticker_input) ? "fan5_01" : String(ticker_input)

            print("選擇類型 (weekly/daily, 預設 weekly): ")
            type_input = strip(readline())
            chart_type = isempty(type_input) ? "weekly" : String(type_input)

            # 只顯示 ASCII 圖表
            file_path = "data_amibroker/fan5/g1/$(ticker).csv"
            if isfile(file_path)
                try
                    daily_data = load_price_data(file_path)
                    data_to_plot = chart_type == "weekly" ? convert_to_weekly(daily_data) : daily_data
                    plot_ascii_chart(data_to_plot, "$ticker $(chart_type) ASCII 圖表", 80, 20)
                catch e
                    println("❌ ASCII 圖表生成失敗：$e")
                end
            else
                println("❌ 找不到數據文件：$file_path")
            end

        elseif choice == "10"
            # 查看可用股票列表
            show_available_tickers_detailed()

        elseif choice == "11"
            # 批量生成圖表
            batch_generate_charts()

        elseif choice == "12"
            # 圖表比較模式
            compare_charts_mode()

        elseif choice == "13"
            println("👋 再見！")
            break

        else
            println("❌ 無效選擇，請輸入 1-13")
        end
    end
end

"""
顯示詳細的可用股票列表
"""
function show_available_tickers_detailed()
    available_tickers = get_available_tickers()

    if isempty(available_tickers)
        println("❌ 找不到可用的股票數據")
        return
    end

    println("\n📋 可用股票詳細列表 ($(length(available_tickers)) 個)：")
    println("="^60)

    for (i, ticker) in enumerate(available_tickers)
        file_path = "data_amibroker/fan5/g1/$(ticker).csv"

        try
            # 快速讀取文件信息
            df = CSV.read(file_path, DataFrame, limit=1)
            file_size = filesize(file_path)

            # 計算總行數（估算）
            total_lines = countlines(file_path) - 1  # 減去標題行

            @printf("%-3d. %-12s (%-8s, %4d 行)\n",
                    i, ticker,
                    file_size < 1024 ? "$(file_size)B" :
                    file_size < 1024*1024 ? "$(round(file_size/1024, digits=1))KB" :
                    "$(round(file_size/(1024*1024), digits=1))MB",
                    total_lines)

            # 每20個換行
            if i % 20 == 0 && i < length(available_tickers)
                print("按 Enter 繼續...")
                readline()
            end

        catch e
            @printf("%-3d. %-12s (錯誤: %s)\n", i, ticker, string(e)[1:min(20, length(string(e)))])
        end
    end

    println("\n💡 提示：選擇任意股票代碼用於圖表分析")
end

"""
批量生成圖表
"""
function batch_generate_charts()
    println("\n🔄 批量圖表生成")
    println("-"^25)

    available_tickers = get_available_tickers()
    if isempty(available_tickers)
        println("❌ 找不到可用的股票數據")
        return
    end

    print("請輸入要生成圖表的股票數量 (1-$(min(10, length(available_tickers))), 預設 3): ")
    count_input = strip(readline())

    count = 3
    if !isempty(count_input)
        try
            count = parse(Int, count_input)
            count = max(1, min(count, 10, length(available_tickers)))
        catch
            println("⚠️  無效輸入，使用預設值 3")
            count = 3
        end
    end

    print("選擇圖表類型 (professional/ma/distribution, 預設 professional): ")
    style_input = strip(readline())
    chart_style = isempty(style_input) ? "professional" : String(style_input)

    print("選擇時間框架 (weekly/daily, 預設 weekly): ")
    time_input = strip(readline())
    chart_type = isempty(time_input) ? "weekly" : String(time_input)

    println("\n🎨 開始批量生成 $count 個圖表...")

    # 創建保存目錄
    save_dir = "charts/batch_$(Dates.format(now(), "yyyymmdd_HHMMSS"))"
    mkpath(save_dir)

    success_count = 0

    for i in 1:count
        ticker = available_tickers[i]
        println("\n[$i/$count] 處理 $ticker...")

        try
            file_path = "data_amibroker/fan5/g1/$(ticker).csv"
            daily_data = load_price_data(file_path)

            data_to_chart = chart_type == "weekly" ? convert_to_weekly(daily_data) : daily_data
            chart_title = "$ticker $(chart_type == "weekly" ? "週線" : "日線")"

            chart = if chart_style == "professional"
                plot_professional_candlestick(data_to_chart, chart_title, 100)
            elseif chart_style == "ma"
                plot_candlestick_with_ma(data_to_chart, chart_title, [5, 10, 20], 100)
            elseif chart_style == "distribution"
                plot_price_distribution(data_to_chart, "$chart_title 價格分佈")
            else
                plot_professional_candlestick(data_to_chart, chart_title, 100)
            end

            # 保存圖表
            filename = "$(ticker)_$(chart_type)_$(chart_style).html"
            filepath = joinpath(save_dir, filename)
            savefig(chart, filepath)

            println("  ✅ $ticker 圖表已保存")
            success_count += 1

        catch e
            println("  ❌ $ticker 處理失敗：$e")
        end
    end

    println("\n🎉 批量生成完成！")
    println("成功：$success_count/$count")
    println("保存位置：$save_dir")
end

"""
圖表比較模式
"""
function compare_charts_mode()
    println("\n📊 圖表比較模式")
    println("-"^20)

    available_tickers = get_available_tickers()
    if length(available_tickers) < 2
        println("❌ 需要至少 2 個股票才能進行比較")
        return
    end

    println("可用股票：$(join(available_tickers[1:min(10, length(available_tickers))], ", "))")
    if length(available_tickers) > 10
        println("... 還有 $(length(available_tickers) - 10) 個")
    end

    print("請輸入第一個股票代碼: ")
    ticker1_input = strip(readline())
    if isempty(ticker1_input)
        println("❌ 必須輸入股票代碼")
        return
    end
    ticker1 = String(ticker1_input)

    print("請輸入第二個股票代碼: ")
    ticker2_input = strip(readline())
    if isempty(ticker2_input)
        println("❌ 必須輸入股票代碼")
        return
    end
    ticker2 = String(ticker2_input)

    print("選擇時間框架 (weekly/daily, 預設 weekly): ")
    time_input = strip(readline())
    chart_type = isempty(time_input) ? "weekly" : String(time_input)

    try
        # 載入兩個股票的數據
        file_path1 = "data_amibroker/fan5/g1/$(ticker1).csv"
        file_path2 = "data_amibroker/fan5/g1/$(ticker2).csv"

        daily_data1 = load_price_data(file_path1)
        daily_data2 = load_price_data(file_path2)

        data1 = chart_type == "weekly" ? convert_to_weekly(daily_data1) : daily_data1
        data2 = chart_type == "weekly" ? convert_to_weekly(daily_data2) : daily_data2

        # 創建比較圖表
        chart1 = plot_professional_candlestick(data1, "$ticker1 $(chart_type)", 100)
        chart2 = plot_professional_candlestick(data2, "$ticker2 $(chart_type)", 100)

        # 組合顯示
        combined_chart = plot(chart1, chart2, layout=(1,2), size=(2400, 800),
                             plot_title="$ticker1 vs $ticker2 比較")

        display(combined_chart)

        # 顯示比較統計
        println("\n📊 比較統計：")
        println("-"^30)
        show_statistics(data1, ticker1, chart_type)
        println()
        show_statistics(data2, ticker2, chart_type)

        # 保存選項
        save_chart_option(combined_chart, "$(ticker1)_vs_$(ticker2)", chart_type, "comparison")

    catch e
        println("❌ 比較圖表生成失敗：$e")
    end
end

# 主程序入口
if abspath(PROGRAM_FILE) == @__FILE__
    # 檢查是否有命令行參數
    if length(ARGS) >= 1
        ticker = ARGS[1]
        chart_type = length(ARGS) >= 2 ? ARGS[2] : "weekly"
        chart_style = length(ARGS) >= 3 ? ARGS[3] : "professional"
        show_enhanced_chart(ticker, chart_type, chart_style)
    else
        enhanced_interactive_menu()
    end
end
