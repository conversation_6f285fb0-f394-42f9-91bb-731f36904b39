#!/usr/bin/env julia

"""
測試 plot 函數修復
"""

println("🧪 測試 plot 函數修復...")

# 載入修復後的圖表模組
include("src/amibroker_chart_simple.jl")

println("\n📊 當前配置：")
println("後端：$CURRENT_BACKEND")
println("Plots 可用性：$PLOTS_AVAILABLE")

if PLOTS_AVAILABLE
    println("\n🔧 測試 plot 函數是否可用...")
    
    try
        # 測試基本 plot 函數
        x = 1:5
        y = [1, 4, 2, 5, 3]
        p = plot(x, y, title="測試圖表")
        println("✅ plot 函數測試成功")
        
        # 測試 plot! 函數
        plot!(p, x, y .+ 1, label="測試線2")
        println("✅ plot! 函數測試成功")
        
        # 測試 bar 函數
        p2 = bar(x, y, title="測試柱狀圖")
        println("✅ bar 函數測試成功")
        
        # 測試 RGB 顏色
        p3 = plot(x, y, color=RGB(1, 0, 0), title="紅色測試")
        println("✅ RGB 顏色測試成功")
        
        println("\n🎉 所有 Plots 函數都可以正常使用！")
        
        # 測試圖表生成
        println("\n📈 測試完整圖表生成...")
        show_simple_enhanced_chart("fan5_01", "weekly", "candlestick")
        
    catch e
        println("❌ plot 函數測試失敗：$e")
        println("詳細錯誤：")
        showerror(stdout, e, catch_backtrace())
        
        # 嘗試診斷問題
        println("\n🔍 診斷信息：")
        println("plot 函數是否已定義：$(isdefined(Main, :plot))")
        println("Plots 模組是否可用：$(isdefined(Main, :Plots))")
        
        if isdefined(Main, :Plots)
            println("Plots.plot 函數是否可用：$(isdefined(Plots, :plot))")
        end
    end
    
else
    println("❌ Plots.jl 不可用，無法測試")
end

println("\n🎉 測試完成！")
