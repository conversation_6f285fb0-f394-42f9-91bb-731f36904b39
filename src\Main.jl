#!/usr/bin/env julia

"""
AmiBroker圖表模擬器與Wonder Grid彩票分析系統 - 主程式
整合所有功能模組，提供統一的用戶界面
"""

using Plots
using PlotlyJS
using StatsPlots

# 設定繪圖後端
Plots.plotlyjs()

# 載入自定義模組
include("amibroker_chart_simple.jl")
include("LotteryAnalysis.jl")
include("ChartUtils.jl")
include("SkipPatternAnalysis.jl")
include("PairingFrequencyAnalysis.jl")

using .LotteryAnalysis
using .ChartUtils
using .SkipPatternAnalysis
using .PairingFrequencyAnalysis
using Printf, DataFrames

"""
主選單系統
"""
function main_menu()
    println("🎯 AmiBroker圖表模擬器與Wonder Grid分析系統")
    println("="^50)
    
    while true
        println("\n主選單：")
        println("1. Wonder Grid 彩票分析")
        println("2. 號碼統計分析")
        println("3. 有利號碼分析")
        println("4. 配對頻率分析")
        println("5. 🆕 Skip值分析")
        println("6. 🧠 Skip深度分析")
        println("7. 圖表模擬器")
        println("8. 系統自檢")
        println("9. 退出")
        
        print("\n請選擇 (1-9): ")
        choice = strip(readline())

        try
            if choice == "1"
                wonder_grid_analysis()
            elseif choice == "2"
                number_statistics_menu()
            elseif choice == "3"
                favorable_numbers_menu()
            elseif choice == "4"
                pairing_analysis_menu()
            elseif choice == "5"
                skip_analysis_menu()
            elseif choice == "6"
                skip_deep_analysis_menu()
            elseif choice == "7"
                chart_simulator_menu()
            elseif choice == "8"
                system_check()
            elseif choice == "9"
                println("👋 再見！")
                break
            else
                println("❌ 無效選擇，請輸入 1-9")
            end
        catch e
            println("❌ 發生錯誤：$e")
        end
    end
end

"""
Wonder Grid 分析
"""
function wonder_grid_analysis()
    println("\n🎯 Wonder Grid 彩票分析")
    println("-"^30)
    
    # 載入數據
    historical_data = load_real_data()
    if isempty(historical_data)
        println("❌ 無法載入數據")
        return
    end
    
    # 獲取關鍵號碼
    print("請輸入關鍵號碼 (1-39)，或按 Enter 使用預設值 13: ")
    key_input = strip(readline())
    
    key_number = if isempty(key_input)
        13
    else
        try
            num = parse(Int, key_input)
            (1 <= num <= 39) ? num : 13
        catch
            13
        end
    end
    
    println("使用關鍵號碼：$key_number")
    
    # 分析關鍵號碼
    stats = analyze_number_statistics(key_number, historical_data)
    display_number_stats(key_number, stats)
    
    # 生成組合
    combinations = generate_wonder_grid_combinations(key_number, 30)
    display_combinations(combinations, "Wonder Grid 組合 (關鍵號碼: $key_number)")
    
    # 顯示有利號碼分析
    display_favorable_analysis(historical_data)
end

"""
號碼統計分析選單
"""
function number_statistics_menu()
    println("\n📈 號碼統計分析")
    println("-"^20)

    historical_data = load_real_data()
    if isempty(historical_data)
        println("❌ 無法載入數據")
        return
    end

    println("總共有 $(length(historical_data)) 期數據可供分析")

    # 獲取分析範圍
    print("請選擇分析範圍：\n")
    print("1. 全部數據 ($(length(historical_data)) 期)\n")
    print("2. 最近N期數據\n")
    print("請選擇 (1-2): ")

    range_choice = strip(readline())
    selected_row = 1  # 默認從最新期開始
    n_periods = nothing  # 默認全部數據

    if range_choice == "2"
        print("請輸入要分析的期數 (1-$(length(historical_data))): ")
        periods_input = strip(readline())
        try
            n_periods = parse(Int, periods_input)
            if n_periods < 1 || n_periods > length(historical_data)
                println("❌ 期數必須在 1-$(length(historical_data)) 之間，使用全部數據")
                n_periods = nothing
            else
                println("✅ 將分析最近 $n_periods 期數據")
            end
        catch
            println("❌ 無效輸入，使用全部數據")
            n_periods = nothing
        end
    end

    print("請輸入要分析的號碼 (1-39): ")
    number_input = strip(readline())

    try
        number = parse(Int, number_input)
        if 1 <= number <= 39
            stats = analyze_number_statistics_range(number, historical_data, selected_row, n_periods)
            display_number_stats_range(number, stats)
        else
            println("❌ 號碼必須在 1-39 之間")
        end
    catch
        println("❌ 無效的號碼輸入")
    end
end

"""
有利號碼分析選單
"""
function favorable_numbers_menu()
    println("\n🎯 有利號碼分析")
    println("-"^20)

    historical_data = load_real_data()
    if isempty(historical_data)
        println("❌ 無法載入數據")
        return
    end

    println("總共有 $(length(historical_data)) 期數據可供分析")

    # 獲取分析範圍
    print("請選擇分析範圍：\n")
    print("1. 全部數據 ($(length(historical_data)) 期)\n")
    print("2. 最近N期數據\n")
    print("請選擇 (1-2): ")

    range_choice = strip(readline())
    selected_row = 1
    n_periods = nothing

    if range_choice == "2"
        print("請輸入要分析的期數 (1-$(length(historical_data))): ")
        periods_input = strip(readline())
        try
            n_periods = parse(Int, periods_input)
            if n_periods < 1 || n_periods > length(historical_data)
                println("❌ 期數必須在 1-$(length(historical_data)) 之間，使用全部數據")
                n_periods = nothing
            else
                println("✅ 將分析最近 $n_periods 期數據")
            end
        catch
            println("❌ 無效輸入，使用全部數據")
            n_periods = nothing
        end
    end

    display_favorable_analysis_range(historical_data, selected_row, n_periods)
end

"""
高級配對頻率分析選單
"""
function pairing_analysis_menu()
    println("\n🔗 高級配對頻率分析系統")
    println("="^40)

    # 載入數據
    historical_data = load_real_data()
    if isempty(historical_data)
        println("❌ 無法載入數據")
        return
    end

    println("總共有 $(length(historical_data)) 期數據可供分析")

    # 選擇分析類型
    println("\n請選擇分析類型：")
    println("1. 🔍 基礎配對頻率分析")
    println("2. 🧠 高級配對深度分析")
    println("3. 📈 配對趨勢預測分析")
    println("4. 🤖 機器學習配對預測")
    println("5. 🎯 智能配對策略生成")
    println("6. 🔬 多維度配對分析")
    print("請選擇 (1-6): ")

    analysis_choice = strip(readline())

    try
        if analysis_choice == "1"
            basic_pairing_analysis(historical_data)
        elseif analysis_choice == "2"
            advanced_pairing_analysis(historical_data)
        elseif analysis_choice == "3"
            pair_trend_analysis(historical_data)
        elseif analysis_choice == "4"
            ml_pair_prediction(historical_data)
        elseif analysis_choice == "5"
            pair_strategy_generation(historical_data)
        elseif analysis_choice == "6"
            multi_dimensional_analysis(historical_data)
        else
            println("❌ 無效選擇")
        end
    catch e
        println("❌ 分析過程發生錯誤：$e")
    end
end

"""
基礎配對頻率分析
"""
function basic_pairing_analysis(historical_data::Vector{LotteryDraw})
    println("\n🔍 基礎配對頻率分析")
    println("-"^25)

    print("請輸入要分析配對的號碼 (1-39): ")
    number_input = strip(readline())

    try
        number = parse(Int, number_input)
        if 1 <= number <= 39
            # 轉換數據格式
            data_matrix = []
            for draw in historical_data
                push!(data_matrix, draw.numbers)
            end

            df_data = DataFrame()
            for i in 1:5
                df_data[!, Symbol("num$i")] = [row[i] for row in data_matrix]
            end

            results = PairingFrequencyAnalysis.analyze_pairing_frequency(df_data, number)

            if isempty(results)
                println("\n號碼 $number 未與任何其他號碼配對過。")
                return
            end

            println("\n📊 與號碼 $number 配對的頻率分析：")
            println("="^40)
            println("排名  號碼  配對次數  配對率")
            println("-"^30)

            total_appearances = sum(number in draw.numbers for draw in historical_data)

            for (rank, (num, freq)) in enumerate(results[1:min(15, length(results))])
                pair_rate = round(freq / total_appearances * 100, digits=1)
                @printf "%3d   %3d    %5d    %5.1f%%\n" rank num freq pair_rate
            end
            println("="^40)
            println("號碼 $number 總出現次數：$total_appearances")

        else
            println("❌ 號碼必須在 1-39 之間")
        end
    catch e
        println("❌ 分析錯誤：$e")
    end
end

"""
高級配對深度分析
"""
function advanced_pairing_analysis(historical_data::Vector{LotteryDraw})
    println("\n🧠 高級配對深度分析")
    println("-"^25)

    print("請輸入要分析的號碼 (1-39): ")
    number_input = strip(readline())

    print("請輸入分析期數 (留空使用全部數據): ")
    periods_input = strip(readline())

    try
        number = parse(Int, number_input)
        if !(1 <= number <= 39)
            println("❌ 號碼必須在 1-39 之間")
            return
        end

        analysis_periods = if isempty(periods_input)
            nothing
        else
            parse(Int, periods_input)
        end

        # 執行高級分析
        analysis = PairingFrequencyAnalysis.analyze_advanced_pairing(historical_data, number, analysis_periods)

        # 顯示分析結果
        display_advanced_pairing_results(analysis)

    catch e
        println("❌ 分析錯誤：$e")
    end
end

"""
顯示高級配對分析結果
"""
function display_advanced_pairing_results(analysis::PairingFrequencyAnalysis.AdvancedPairAnalysis)
    println("\n📊 高級配對分析結果")
    println("="^60)
    println("目標號碼：$(analysis.target_number)")
    println("分析期數：$(analysis.analysis_period)")
    println("目標號碼出現次數：$(analysis.total_appearances)")
    println("分析日期：$(analysis.analysis_date)")

    # 配對頻率排行
    println("\n🔥 配對頻率排行榜 (前15名)：")
    println("="^70)
    println("排名  號碼  配對次數  配對機率  配對強度  相關性  預測信心")
    println("-"^70)

    sorted_pairs = sort(collect(analysis.pair_frequencies), by=x->x[2], rev=true)

    for (rank, (num, freq)) in enumerate(sorted_pairs[1:min(15, length(sorted_pairs))])
        prob = get(analysis.pair_probabilities, num, 0.0)
        strength = get(analysis.pair_strengths, num, 0.0)
        correlation = get(analysis.correlation_scores, num, 0.0)
        confidence = get(analysis.prediction_confidence, num, 0.0)

        @printf "%3d   %3d    %5d     %5.1f%%   %6.2f    %6.2f   %6.2f\n" rank num freq (prob*100) strength correlation confidence
    end

    # 時間模式分析
    if !get(analysis.temporal_patterns, "insufficient_data", false)
        println("\n⏰ 時間模式分析：")
        println("="^40)
        patterns = analysis.temporal_patterns

        if haskey(patterns, "average_interval")
            println("平均出現間隔：$(round(patterns["average_interval"], digits=1)) 期")
            println("間隔標準差：$(round(patterns["interval_std"], digits=1))")
            println("最短間隔：$(patterns["min_interval"]) 期")
            println("最長間隔：$(patterns["max_interval"]) 期")
        end

        if haskey(patterns, "periodicity") && patterns["periodicity"]["detected"]
            period_info = patterns["periodicity"]
            println("🔄 檢測到週期性模式：")
            println("  週期長度：$(period_info["period"]) 期")
            println("  信心度：$(round(period_info["confidence"]*100, digits=1))%")
        end

        if haskey(patterns, "recent_trend")
            trend = patterns["recent_trend"]
            trend_desc = trend > 0 ? "間隔增加（出現頻率下降）" : "間隔減少（出現頻率上升）"
            println("📈 最近趨勢：$trend_desc")
            println("  趨勢顯著性：$(round(patterns["trend_significance"], digits=2))")
        end
    end

    # 推薦配對
    println("\n💡 推薦配對組合：")
    println("="^30)

    # 選出高信心度的配對
    high_confidence_pairs = []
    for (num, confidence) in analysis.prediction_confidence
        if confidence > 0.6 && haskey(analysis.pair_frequencies, num)
            push!(high_confidence_pairs, (num, confidence, analysis.pair_frequencies[num]))
        end
    end

    sort!(high_confidence_pairs, by=x->x[2], rev=true)

    if !isempty(high_confidence_pairs)
        println("高信心度配對 (信心度>60%)：")
        for (i, (num, confidence, freq)) in enumerate(high_confidence_pairs[1:min(8, length(high_confidence_pairs))])
            println("  $(analysis.target_number)-$(num)：信心度 $(round(confidence*100, digits=1))%，歷史配對 $(freq) 次")
        end
    else
        println("當前沒有高信心度的推薦配對")
    end
end

"""
配對趨勢預測分析
"""
function pair_trend_analysis(historical_data::Vector{LotteryDraw})
    println("\n📈 配對趨勢預測分析")
    println("-"^25)

    print("請輸入第一個號碼 (1-39): ")
    num1_input = strip(readline())
    print("請輸入第二個號碼 (1-39): ")
    num2_input = strip(readline())

    try
        num1 = parse(Int, num1_input)
        num2 = parse(Int, num2_input)

        if !(1 <= num1 <= 39) || !(1 <= num2 <= 39)
            println("❌ 號碼必須在 1-39 之間")
            return
        end

        if num1 == num2
            println("❌ 請輸入兩個不同的號碼")
            return
        end

        # 執行趨勢分析
        trend_analysis = PairingFrequencyAnalysis.analyze_pair_trends(historical_data, (num1, num2))

        # 顯示結果
        display_pair_trend_results(trend_analysis)

    catch e
        println("❌ 分析錯誤：$e")
    end
end

"""
顯示配對趨勢分析結果
"""
function display_pair_trend_results(trend::PairingFrequencyAnalysis.PairTrendAnalysis)
    println("\n📊 配對趨勢分析結果")
    println("="^50)

    num1, num2 = trend.number_pair
    println("分析配對：$(num1) - $(num2)")
    println("趨勢方向：$(trend.trend_direction)")
    println("趨勢強度：$(round(trend.trend_strength * 100, digits=1))%")
    println("最近頻率：$(round(trend.recent_frequency * 100, digits=2))%")
    println("歷史平均：$(round(trend.historical_average * 100, digits=2))%")
    println("波動性：$(round(trend.volatility, digits=2))")
    println("信心度：$(round(trend.confidence_level * 100, digits=1))%")

    if trend.cycle_length !== nothing
        println("檢測週期：$(trend.cycle_length) 期")
    end

    if trend.next_expected_appearance !== nothing
        println("預期下次出現：第 $(trend.next_expected_appearance) 期")
    end

    # 投資建議
    println("\n💡 投資建議：")
    if trend.confidence_level > 0.7
        if trend.trend_direction == "上升"
            println("🔥 強烈推薦：上升趨勢明確，建議重點關注")
        elseif trend.trend_direction == "週期性"
            println("⭐ 推薦：週期性模式穩定，適合定期投注")
        else
            println("✅ 可考慮：趨勢穩定，適合保守投注")
        end
    elseif trend.confidence_level > 0.4
        println("⚠️  謹慎考慮：信心度中等，建議小額測試")
    else
        println("❌ 不推薦：信心度較低，建議觀望")
    end
end

"""
機器學習配對預測
"""
function ml_pair_prediction(historical_data::Vector{LotteryDraw})
    println("\n🤖 機器學習配對預測")
    println("-"^25)

    print("請輸入目標號碼 (1-39): ")
    number_input = strip(readline())

    print("請輸入預測期數 (建議5-15期): ")
    horizon_input = strip(readline())

    try
        number = parse(Int, number_input)
        horizon = isempty(horizon_input) ? 10 : parse(Int, horizon_input)

        if !(1 <= number <= 39)
            println("❌ 號碼必須在 1-39 之間")
            return
        end

        if !(1 <= horizon <= 50)
            println("❌ 預測期數必須在 1-50 之間")
            return
        end

        # 執行機器學習預測
        prediction = PairingFrequencyAnalysis.predict_pair_emergence(historical_data, number, horizon)

        # 顯示預測結果
        display_ml_prediction_results(prediction)

    catch e
        println("❌ 預測錯誤：$e")
    end
end

"""
顯示機器學習預測結果
"""
function display_ml_prediction_results(prediction::PairingFrequencyAnalysis.PairPrediction)
    println("\n🤖 機器學習預測結果")
    println("="^50)

    println("目標號碼：$(prediction.target_number)")
    println("預測方法：$(prediction.prediction_method)")
    println("整體信心度：$(round(prediction.confidence_score * 100, digits=1))%")
    println("風險評估：$(prediction.risk_assessment)")
    println("期望回報：$(round(prediction.expected_return, digits=2))")

    println("\n🎯 預測配對排行榜：")
    println("="^40)
    println("排名  號碼  預測機率  推薦度")
    println("-"^30)

    for (rank, (num, prob)) in enumerate(prediction.predicted_pairs)
        recommendation = if prob > 0.6
            "強烈推薦"
        elseif prob > 0.4
            "推薦"
        elseif prob > 0.25
            "可考慮"
        else
            "謹慎"
        end

        @printf "%3d   %3d    %6.1f%%   %s\n" rank num (prob*100) recommendation
    end

    println("\n💡 系統建議：")
    println(prediction.recommendation)
end

"""
智能配對策略生成
"""
function pair_strategy_generation(historical_data::Vector{LotteryDraw})
    println("\n🎯 智能配對策略生成")
    println("-"^25)

    println("請選擇策略類型：")
    println("1. 保守策略 (低風險，穩定回報)")
    println("2. 激進策略 (高風險，高回報)")
    println("3. 平衡策略 (中等風險回報)")
    println("4. 全部策略 (生成所有類型)")
    print("請選擇 (1-4): ")

    strategy_choice = strip(readline())

    strategy_type = if strategy_choice == "1"
        "conservative"
    elseif strategy_choice == "2"
        "aggressive"
    elseif strategy_choice == "3"
        "balanced"
    else
        "balanced"  # 默認生成全部
    end

    try
        # 生成策略
        strategies = PairingFrequencyAnalysis.generate_pair_strategies(historical_data, strategy_type)

        # 顯示策略結果
        display_strategy_results(strategies)

    catch e
        println("❌ 策略生成錯誤：$e")
    end
end

"""
顯示策略結果
"""
function display_strategy_results(strategies::Vector{PairingFrequencyAnalysis.PairStrategy})
    for (i, strategy) in enumerate(strategies)
        println("\n🎯 策略 $(i)：$(strategy.strategy_name)")
        println("="^50)
        println("策略類型：$(strategy.strategy_type)")
        println("風險等級：$(strategy.risk_level)")
        println("預期命中率：$(round(strategy.expected_hit_rate * 100, digits=1))%")

        println("\n📋 目標號碼池：")
        println(join(lpad.(strategy.target_numbers, 2), " "))

        println("\n🔗 推薦配對組合 (前10個)：")
        for (j, (num1, num2)) in enumerate(strategy.recommended_pairs[1:min(10, length(strategy.recommended_pairs))])
            println("  $(j). $(num1)-$(num2)")
        end

        println("\n💰 投資建議：")
        println("$(strategy.investment_suggestion)")

        println("\n🛑 止損標準：")
        println("$(strategy.stop_loss_criteria)")

        if i < length(strategies)
            println("\n" * "-"^50)
        end
    end
end

"""
多維度配對分析
"""
function multi_dimensional_analysis(historical_data::Vector{LotteryDraw})
    println("\n🔬 多維度配對分析")
    println("-"^20)

    println("請選擇分析深度：")
    println("1. 標準分析 (配對強度矩陣 + 相關性網絡)")
    println("2. 深度分析 (+ 時間序列分析)")
    println("3. 全面分析 (+ 聚類分析)")
    print("請選擇 (1-3): ")

    depth_choice = strip(readline())

    analysis_depth = if depth_choice == "1"
        "standard"
    elseif depth_choice == "2"
        "deep"
    else
        "comprehensive"
    end

    try
        println("\n🔄 執行多維度分析，請稍候...")

        # 執行多維度分析
        results = PairingFrequencyAnalysis.analyze_multi_dimensional_pairs(historical_data, analysis_depth)

        # 顯示分析結果
        display_multi_dimensional_results(results, analysis_depth)

    catch e
        println("❌ 多維度分析錯誤：$e")
    end
end

"""
顯示多維度分析結果
"""
function display_multi_dimensional_results(results::Dict{String, Any}, depth::String)
    println("\n📊 多維度配對分析結果")
    println("="^50)

    # 1. 配對強度矩陣摘要
    if haskey(results, "pair_strength_matrix")
        matrix = results["pair_strength_matrix"]
        max_strength = maximum(matrix)
        avg_strength = mean(matrix[matrix .> 0])

        println("🔗 配對強度矩陣摘要：")
        println("  最強配對強度：$(round(max_strength, digits=3))")
        println("  平均配對強度：$(round(avg_strength, digits=3))")

        # 找出最強的配對
        strongest_pairs = []
        for i in 1:39
            for j in (i+1):39
                if matrix[i, j] > avg_strength * 1.5
                    push!(strongest_pairs, (i, j, matrix[i, j]))
                end
            end
        end

        sort!(strongest_pairs, by=x->x[3], rev=true)

        if !isempty(strongest_pairs)
            println("\n  🔥 最強配對組合 (前8個)：")
            for (i, (num1, num2, strength)) in enumerate(strongest_pairs[1:min(8, length(strongest_pairs))])
                println("    $(i). $(num1)-$(num2)：強度 $(round(strength, digits=3))")
            end
        end
    end

    # 2. 相關性網絡
    if haskey(results, "correlation_network")
        network = results["correlation_network"]
        density = network["network_density"]
        strong_corr = network["strong_correlations"]

        println("\n🕸️  相關性網絡分析：")
        println("  網絡密度：$(round(density * 100, digits=1))%")
        println("  強相關配對數：$(length(strong_corr))")

        if !isempty(strong_corr)
            println("\n  📈 強相關配對 (前5個)：")
            sorted_corr = sort(collect(strong_corr), by=x->abs(x[2]), rev=true)
            for (i, ((num1, num2), corr)) in enumerate(sorted_corr[1:min(5, length(sorted_corr))])
                corr_type = corr > 0 ? "正相關" : "負相關"
                println("    $(i). $(num1)-$(num2)：$(corr_type) $(round(abs(corr), digits=3))")
            end
        end
    end

    # 3. 異常檢測
    if haskey(results, "anomaly_detection")
        anomalies = results["anomaly_detection"]

        println("\n⚠️  配對異常檢測：")
        println("  檢測到異常配對：$(length(anomalies)) 個")

        if !isempty(anomalies)
            println("\n  🚨 顯著異常配對 (前5個)：")
            for (i, anomaly) in enumerate(anomalies[1:min(5, length(anomalies))])
                num1, num2 = anomaly["pair"]
                println("    $(i). $(num1)-$(num2)：$(anomaly["type"])")
                println("       實際：$(anomaly["actual_count"]) 次，期望：$(anomaly["expected_count"]) 次")
                println("       偏差分數：$(anomaly["deviation_score"])，顯著性：$(anomaly["significance"])")
            end
        end
    end

    println("\n💡 分析建議：")
    println("  - 重點關注配對強度高且相關性強的組合")
    println("  - 異常高頻配對可能存在隱藏規律，值得深入研究")
    println("  - 異常低頻配對可能即將反彈，適合逆向投資")

    if depth == "comprehensive"
        println("  - 建議結合聚類分析結果制定投資組合")
    end
end


"""
Skip值分析選單
"""
function skip_analysis_menu()
    println("\n🔢 Skip值分析")
    println("-"^15)

    historical_data = load_real_data()
    if isempty(historical_data)
        println("❌ 無法載入數據")
        return
    end

    println("總共有 $(length(historical_data)) 期數據可供分析")

    # 選擇分析模式
    println("\n請選擇分析模式：")
    println("1. 單期Skip值查詢")
    println("2. Skip模式分析 (分析多期)")
    println("3. Skip統計摘要")
    print("請選擇 (1-3): ")

    mode_choice = strip(readline())

    if mode_choice == "1"
        single_period_skip_analysis(historical_data)
    elseif mode_choice == "2"
        multi_period_skip_analysis(historical_data)
    elseif mode_choice == "3"
        skip_statistics_summary(historical_data)
    else
        println("❌ 無效選擇")
    end
end

"""
單期Skip值查詢
"""
function single_period_skip_analysis(historical_data::Vector{LotteryDraw})
    println("\n🔍 單期Skip值查詢")
    println("-"^20)

    print("請輸入要查詢的期數 (1為最新期，$(length(historical_data))為最早期): ")
    period_input = strip(readline())

    try
        period = parse(Int, period_input)
        if period < 1 || period > length(historical_data)
            println("❌ 期數必須在 1-$(length(historical_data)) 之間")
            return
        end

        if period == length(historical_data)
            println("❌ 最早期無法計算Skip值（沒有更早的歷史數據）")
            return
        end

        # 計算Skip值
        draw = historical_data[period]
        skip_values = calculate_skip_values_for_draw(period, historical_data)

        println("\n📊 第 $period 期 Skip值分析")
        println("日期：$(draw.draw_date)")
        println("開獎號碼：$(join([lpad(n, 2) for n in draw.numbers], " "))")
        println("Skip值：  $(join([lpad(s, 2) for s in skip_values], " "))")

        # 計算統計
        total_skip = sum(skip_values)
        avg_skip = round(total_skip / length(skip_values), digits=2)
        min_skip = minimum(skip_values)
        max_skip = maximum(skip_values)
        skip_range = max_skip - min_skip

        println("\n統計摘要：")
        println("  總Skip值：$(total_skip)")
        println("  平均Skip：$(avg_skip)")
        println("  Skip範圍：$(min_skip) - $(max_skip) (範圍：$(skip_range))")

        # 分析每個號碼的Skip特徵
        println("\n詳細分析：")
        for (i, (number, skip)) in enumerate(zip(draw.numbers, skip_values))
            if skip == 0
                println("  號碼 $(number)：Skip=$(skip) (連續開出)")
            elseif skip <= 2
                println("  號碼 $(number)：Skip=$(skip) (短期重現)")
            elseif skip <= 5
                println("  號碼 $(number)：Skip=$(skip) (中期重現)")
            else
                println("  號碼 $(number)：Skip=$(skip) (長期重現)")
            end
        end

    catch
        println("❌ 無效的期數輸入")
    end
end

"""
多期Skip模式分析
"""
function multi_period_skip_analysis(historical_data::Vector{LotteryDraw})
    println("\n📈 多期Skip模式分析")
    println("-"^20)

    print("請輸入要分析的期數 (建議不超過100期): ")
    periods_input = strip(readline())

    try
        periods = parse(Int, periods_input)
        if periods < 1
            println("❌ 期數必須大於 0")
            return
        end

        max_periods = min(periods, length(historical_data) - 1, 100)
        if max_periods != periods
            println("⚠️  調整分析期數為 $(max_periods) 期")
        end

        # 執行Skip分析
        skip_results = analyze_skip_patterns(historical_data, max_periods)

        if !isempty(skip_results)
            display_skip_analysis(skip_results, min(20, max_periods))
        else
            println("❌ Skip分析失敗")
        end

    catch
        println("❌ 無效的期數輸入")
    end
end

"""
Skip統計摘要
"""
function skip_statistics_summary(historical_data::Vector{LotteryDraw})
    println("\n📊 Skip統計摘要")
    println("-"^15)

    print("請輸入統計期數 (建議50-200期): ")
    periods_input = strip(readline())

    try
        periods = parse(Int, periods_input)
        if periods < 1
            println("❌ 期數必須大於 0")
            return
        end

        max_periods = min(periods, length(historical_data) - 1)
        if max_periods != periods
            println("⚠️  調整統計期數為 $(max_periods) 期")
        end

        # 執行Skip分析
        skip_results = analyze_skip_patterns(historical_data, max_periods)

        if !isempty(skip_results)
            stats = get_skip_statistics(skip_results)

            println("\n📈 Skip統計摘要 (基於 $(max_periods) 期數據)")
            println("="^50)

            println("🔢 Skip值分佈：")
            println("  最小值：$(stats["skip_min"])")
            println("  最大值：$(stats["skip_max"])")
            println("  平均值：$(stats["skip_mean"])")
            println("  中位數：$(stats["skip_median"])")
            println("  標準差：$(stats["skip_std"])")

            println("\n📊 每期總Skip統計：")
            println("  最小總Skip：$(stats["total_skip_min"])")
            println("  最大總Skip：$(stats["total_skip_max"])")
            println("  平均總Skip：$(stats["total_skip_mean"])")
            println("  中位數總Skip：$(stats["total_skip_median"])")

            println("\n🎯 Skip範圍統計：")
            println("  最小範圍：$(stats["range_min"])")
            println("  最大範圍：$(stats["range_max"])")
            println("  平均範圍：$(stats["range_mean"])")

            println("\n🔥 最常見Skip值：")
            for (i, (skip, count)) in enumerate(stats["most_common_skip"][1:min(10, length(stats["most_common_skip"]))])
                percentage = round(count / stats["total_skip_values"] * 100, digits=1)
                println("  第$(i)名：Skip=$skip (出現 $count 次，佔 $percentage%)")
            end

        else
            println("❌ Skip統計分析失敗")
        end

    catch
        println("❌ 無效的期數輸入")
    end
end

"""
獲取可用的股票代碼列表
"""
function get_available_tickers()::Vector{String}
    base_dir = "data_amibroker/fan5/g1"

    if !isdir(base_dir)
        println("❌ 數據目錄不存在：$base_dir")
        return String[]
    end

    files = readdir(base_dir)
    csv_files = filter(f -> endswith(f, ".csv"), files)

    # 提取股票代碼（去掉 .csv 擴展名）
    tickers = [replace(f, ".csv" => "") for f in csv_files]

    return sort(tickers)
end

"""
圖表模擬器選單
"""
function chart_simulator_menu()
    # 檢查是否有圖表數據
    if !isdir("data_amibroker")
        println("❌ 找不到圖表數據目錄")
        return
    end

    

    # 檢查 Plots.jl 是否可用
    plots_available = PLOTS_AVAILABLE

    while true
        println("\n📊 AmiBroker 圖表模擬器")
        println("="^40)

        if plots_available
            println("🕯️  專業蠟燭圖（漲紅跌綠）：")
            println("1. 真正蠟燭圖（週線）")
            println("2. 真正蠟燭圖（日線）")
            println("3. 蠟燭圖+移動平均線（週線）")
            println("4. 蠟燭圖+移動平均線（日線）")
            println()
            println("📈 其他專業圖表：")
            println("5. 簡化專業圖表（週線）")
            println("6. 簡化專業圖表（日線）")
            println("7. 價格分佈直方圖")
            println()
        else
            println("⚠️  Plots.jl 不可用，僅提供文字版功能")
        end

        println("📝 文字版選項：")
        println("8. 文字版蠟燭圖（週線）")
        println("9. 文字版蠟燭圖（日線）")
        println("10. ASCII 圖表")
        println()
        println("🔧 其他功能：")
        println("11. 查看可用股票列表")
        println("12. 返回主選單")

        print("\n請選擇 (1-12): ")
        choice = strip(readline())

        try
            if choice in ["1", "2", "3", "4", "5", "6", "7"] && plots_available
                # 專業圖表選項
                print("請輸入股票代碼 (預設 fan5_01): ")
                ticker_input = strip(readline())
                ticker = isempty(ticker_input) ? "fan5_01" : String(ticker_input)

                chart_type = choice in ["1", "3", "5"] ? "weekly" : "daily"
                chart_style = if choice in ["1", "2"]
                    "candlestick"  # 真正蠟燭圖
                elseif choice in ["3", "4"]
                    "candlestick_ma"  # 蠟燭圖+移動平均線
                elseif choice in ["5", "6"]
                    "simple"  # 簡化專業圖表
                elseif choice == "7"
                    "histogram"
                end

                # 調用圖表繪製函數
                show_simple_enhanced_chart(ticker, chart_type, chart_style)

            elseif choice in ["8", "9", "10"]
                # 文字版圖表
                print("請輸入股票代碼 (預設 fan5_01): ")
                ticker_input = strip(readline())
                ticker = isempty(ticker_input) ? "fan5_01" : String(ticker_input)

                if choice == "8"
                    show_simple_enhanced_chart(ticker, "weekly", "text")
                elseif choice == "9"
                    show_simple_enhanced_chart(ticker, "daily", "text")
                elseif choice == "10"
                    # ASCII 圖表功能
                    file_path = "data_amibroker/fan5/g1/$(ticker).csv"
                    if isfile(file_path)
                        data = load_price_data(file_path)
                        if !isempty(data)
                            plot_ascii_chart(data, "$(ticker) ASCII圖表", 80, 15)
                        else
                            println("❌ 無法載入數據")
                        end
                    else
                        println("❌ 找不到數據文件：$file_path")
                    end
                end

            elseif choice == "11"
                # 查看可用股票列表
                available_tickers = get_available_tickers()
                if !isempty(available_tickers)
                    println("\n📋 可用的股票代碼：")
                    for (i, ticker) in enumerate(available_tickers[1:min(20, length(available_tickers))])
                        println("  $i. $ticker")
                    end
                    if length(available_tickers) > 20
                        println("  ... 還有 $(length(available_tickers) - 20) 個")
                    end
                else
                    println("❌ 找不到可用的股票數據")
                end

            elseif choice == "12"
                println("📤 返回主選單")
                break

            elseif choice in ["1", "2", "3", "4", "5", "6", "7"] && !plots_available
                println("❌ Plots.jl 不可用，請選擇文字版選項 (8-10)")

            else
                println("❌ 無效選擇，請輸入 1-12")
            end

        catch e
            println("❌ 處理選項時發生錯誤：$e")
            println("請重試或選擇其他選項")
        end
    end
end

"""
系統自檢
"""
function system_check()
    println("\n🔧 系統自檢")
    println("-"^10)
    
    # 檢查數據文件
    println("檢查數據文件...")
    if isfile("data/fan5.csv")
        println("✅ 彩票數據文件存在")
    else
        println("❌ 彩票數據文件不存在")
    end
    
    # 檢查圖表數據目錄
    if isdir("data_amibroker")
        println("✅ 圖表數據目錄存在")
    else
        println("❌ 圖表數據目錄不存在")
    end
    
    # 檢查繪圖包
    try
        @eval using Plots
        println("✅ Plots.jl 可用")
    catch
        println("⚠️  Plots.jl 不可用，僅支援文字圖表")
    end
    
    # 測試數據載入
    println("測試數據載入...")
    try
        data = load_real_data()
        if !isempty(data)
            println("✅ 數據載入成功 ($(length(data)) 期)")
        else
            println("❌ 數據載入失敗")
        end
    catch e
        println("❌ 數據載入錯誤：$e")
    end
end

"""
顯示號碼統計信息
"""
function display_number_stats(number::Int, stats::Dict{String,Any})
    println("\n號碼 $number 的統計資訊：")
    println("  出現次數：$(stats["appearances"]) / $(stats["total_draws"])")
    println("  出現頻率：$(stats["frequency"]) (理論值：$(stats["theoretical_frequency"]))")
    println("  當前 Skip：$(stats["current_skip"])")
    println("  理論 FFG：$(stats["theoretical_ffg"])")
    println("  實際 FFG：$(stats["empirical_ffg"])")
    println("  平均 Skip：$(stats["avg_skip"]) (範圍：$(stats["min_skip"])-$(stats["max_skip"]))")
    println("  冷熱評分：$(stats["heat_score"])/100")
    println("  時機評分：$(stats["timing_score"])/100")
    println("  是否有利：$(stats["is_favorable"] ? "是" : "否")")
    if stats["last_appearance_date"] !== nothing
        println("  上次開出：$(stats["last_appearance_date"])")
    end
end

"""
顯示號碼統計信息（範圍版本）
"""
function display_number_stats_range(number::Int, stats::Dict{String,Any})
    # 顯示分析範圍信息
    if haskey(stats, "analysis_range")
        range_info = stats["analysis_range"]
        println("\n📊 分析範圍信息：")
        if range_info["n_periods"] == range_info["actual_periods"]
            if range_info["n_periods"] == length(stats["total_draws"]) || range_info["start_date"] === nothing
                println("  範圍：全部數據 ($(range_info["actual_periods"]) 期)")
            else
                println("  範圍：最近 $(range_info["n_periods"]) 期")
                println("  日期範圍：$(range_info["start_date"]) 到 $(range_info["end_date"])")
            end
        else
            println("  請求期數：$(range_info["n_periods"])")
            println("  實際期數：$(range_info["actual_periods"])")
            println("  日期範圍：$(range_info["start_date"]) 到 $(range_info["end_date"])")
        end
    end

    # 顯示統計信息
    display_number_stats(number, stats)
end

"""
顯示有利號碼分析
"""
function display_favorable_analysis(historical_data::Vector{LotteryDraw})
    println("\n📈 有利號碼分析")
    println("="^50)

    favorable_numbers = find_favorable_numbers(historical_data, 15)

    println("排名  號碼  綜合評分  時機評分  冷熱評分  當前Skip  實際FFG")
    println("-"^60)

    for (rank, (number, score)) in enumerate(favorable_numbers)
        stats = analyze_number_statistics(number, historical_data)
        println("$(lpad(rank, 3))   $(lpad(number, 2))    $(lpad(round(score, digits=1), 5))    $(lpad(stats["timing_score"], 5))    $(lpad(stats["heat_score"], 5))     $(lpad(stats["current_skip"], 4))    $(lpad(stats["empirical_ffg"], 5))")
    end

    println("\n💡 建議重點關注前 10 名號碼")
    top_10 = [num for (num, _) in favorable_numbers[1:10]]
    println("重點號碼：$(join(lpad.(top_10, 2), " "))")
end

"""
顯示有利號碼分析（範圍版本）
"""
function display_favorable_analysis_range(historical_data::Vector{LotteryDraw}, selected_row::Int=1, n_periods::Union{Int,Nothing}=nothing)
    # 獲取範圍數據
    range_data = get_recent_data(historical_data, selected_row, n_periods)

    # 顯示分析範圍信息
    println("\n📊 分析範圍信息：")
    if n_periods === nothing
        println("  範圍：全部數據 ($(length(range_data)) 期)")
    else
        println("  範圍：最近 $n_periods 期")
        println("  實際期數：$(length(range_data))")
    end
    if !isempty(range_data)
        println("  日期範圍：$(range_data[end].draw_date) 到 $(range_data[1].draw_date)")
    end

    println("\n📈 有利號碼分析")
    println("="^50)

    favorable_numbers = find_favorable_numbers(range_data, 15)

    println("排名  號碼  綜合評分  時機評分  冷熱評分  當前Skip  實際FFG")
    println("-"^60)

    for (rank, (number, score)) in enumerate(favorable_numbers)
        stats = analyze_number_statistics(number, range_data)
        println("$(lpad(rank, 3))   $(lpad(number, 2))    $(lpad(round(score, digits=1), 5))    $(lpad(stats["timing_score"], 5))    $(lpad(stats["heat_score"], 5))     $(lpad(stats["current_skip"], 4))    $(lpad(stats["empirical_ffg"], 5))")
    end

    println("\n💡 建議重點關注前 10 名號碼")
    top_10 = [num for (num, _) in favorable_numbers[1:10]]
    println("重點號碼：$(join(lpad.(top_10, 2), " "))")

    # 與全部數據的比較
    if n_periods !== nothing && length(range_data) < length(historical_data)
        println("\n🔄 與全部數據比較：")
        all_favorable = find_favorable_numbers(historical_data, 10)
        all_top_10 = [num for (num, _) in all_favorable[1:10]]

        common_numbers = intersect(top_10, all_top_10)
        println("  共同熱門號碼：$(join(lpad.(common_numbers, 2), " ")) ($(length(common_numbers))/10)")

        new_numbers = setdiff(top_10, all_top_10)
        if !isempty(new_numbers)
            println("  近期新興號碼：$(join(lpad.(new_numbers, 2), " "))")
        end
    end
end

"""
生成 Wonder Grid 組合（簡化版）
"""
function generate_wonder_grid_combinations(key_number::Int, max_combinations::Int=50)::Vector{Vector{Int}}
    if !(1 <= key_number <= 39)
        throw(ArgumentError("關鍵號碼必須在 1-39 之間"))
    end
    
    combinations = Vector{Int}[]
    
    # 基於關鍵號碼生成 FFG 序列
    ffg_numbers = Int[]
    current = key_number
    
    for i in 1:15
        push!(ffg_numbers, current)
        current = (current + 7) % 39 + 1
    end
    
    ffg_numbers = sort(unique(ffg_numbers))
    
    # 生成組合
    if length(ffg_numbers) >= 5
        for i in 1:length(ffg_numbers)-4
            for j in i+1:length(ffg_numbers)-3
                for k in j+1:length(ffg_numbers)-2
                    for l in k+1:length(ffg_numbers)-1
                        for m in l+1:length(ffg_numbers)
                            combination = [ffg_numbers[i], ffg_numbers[j], ffg_numbers[k], ffg_numbers[l], ffg_numbers[m]]
                            push!(combinations, combination)
                            
                            if length(combinations) >= max_combinations
                                return combinations
                            end
                        end
                    end
                end
            end
        end
    end
    
    return combinations
end

"""
顯示組合
"""
function display_combinations(combinations::Vector{Vector{Int}}, title::String="Wonder Grid 組合")
    println("\n" * "="^50)
    println(title)
    println("="^50)
    
    for (i, combo) in enumerate(combinations[1:min(20, length(combinations))])
        println("$(lpad(i, 3)): $(join(lpad.(combo, 2), " "))")
    end
    
    if length(combinations) > 20
        println("... 還有 $(length(combinations) - 20) 個組合")
    end
    
    println("\n總共生成 $(length(combinations)) 個組合")
end

"""
Skip深度分析選單
"""
function skip_deep_analysis_menu()
    println("\n🧠 Skip深度分析")
    println("-"^18)

    historical_data = load_real_data()
    if isempty(historical_data)
        println("❌ 無法載入數據")
        return
    end

    println("總共有 $(length(historical_data)) 期數據可供分析")

    # 選擇分析類型
    println("\n請選擇深度分析類型：")
    println("1. 🔍 隱藏規律發現")
    println("2. 🔮 號碼趨勢預測")
    println("3. 🎯 選號策略優化")
    println("4. 🔬 交叉驗證分析")
    print("請選擇 (1-4): ")

    analysis_choice = strip(readline())

    if analysis_choice == "1"
        hidden_pattern_analysis(historical_data)
    elseif analysis_choice == "2"
        trend_prediction_analysis(historical_data)
    elseif analysis_choice == "3"
        strategy_optimization_analysis(historical_data)
    elseif analysis_choice == "4"
        cross_validation_analysis(historical_data)
    else
        println("❌ 無效選擇")
    end
end

"""
隱藏規律發現分析
"""
function hidden_pattern_analysis(historical_data::Vector{LotteryDraw})
    println("\n🔍 隱藏規律發現分析")
    println("-"^25)

    print("請輸入分析期數 (建議100-300期): ")
    periods_input = strip(readline())

    try
        periods = parse(Int, periods_input)
        if periods < 50
            println("❌ 期數太少，建議至少50期")
            return
        end

        max_periods = min(periods, length(historical_data) - 1)
        if max_periods != periods
            println("⚠️  調整分析期數為 $(max_periods) 期")
        end

        # 執行隱藏規律發現
        try
            println("Debug: Calling discover_hidden_patterns with $(typeof(historical_data)) and $(typeof(max_periods))")
            patterns = discover_hidden_patterns(historical_data, max_periods)

            if !isempty(patterns)
                println("\n📊 發現的隱藏規律：")
                println("="^60)

                for (i, pattern) in enumerate(patterns)
                    println("$(i). $(pattern.pattern_type)")
                    println("   描述：$(pattern.description)")
                    println("   頻率：$(pattern.frequency) 次")
                    println("   信心度：$(round(pattern.confidence * 100, digits=1))%")
                    println("   預測價值：$(round(pattern.prediction_value, digits=1))")

                    if !isempty(pattern.examples)
                        println("   示例：")
                        for (j, (period, date, skips)) in enumerate(pattern.examples[1:min(2, length(pattern.examples))])
                            skips_str = join([lpad(s, 2) for s in skips], " ")
                            println("     第$(period)期 ($(date))：Skip值 $(skips_str)")
                        end
                    end
                    println()
                end

                # 提供應用建議
                println("💡 應用建議：")
                high_confidence_patterns = [p for p in patterns if p.confidence > 0.6]
                if !isempty(high_confidence_patterns)
                    println("  - 重點關注高信心度規律：$(length(high_confidence_patterns)) 個")
                    for pattern in high_confidence_patterns[1:min(3, length(high_confidence_patterns))]
                        println("    * $(pattern.pattern_type)：$(pattern.description)")
                    end
                else
                    println("  - 當前數據中規律性較弱，建議增加分析期數或結合其他分析方法")
                end

            else
                println("❌ 未發現明顯的隱藏規律")
            end
        catch e
            println("❌ 隱藏規律分析失敗：$e")
        end

    catch
        println("❌ 無效的期數輸入")
    end
end

"""
趨勢預測分析
"""
function trend_prediction_analysis(historical_data::Vector{LotteryDraw})
    println("\n🔮 號碼趨勢預測分析")
    println("-"^25)

    print("請輸入預測基礎期數 (建議50-150期): ")
    periods_input = strip(readline())

    try
        periods = parse(Int, periods_input)
        if periods < 30
            println("❌ 期數太少，建議至少30期")
            return
        end

        max_periods = min(periods, length(historical_data))
        if max_periods != periods
            println("⚠️  調整分析期數為 $(max_periods) 期")
        end

        # 執行趨勢預測
        try
            trends = predict_number_trends(historical_data, max_periods)

            if !isempty(trends)
                println("\n📈 號碼趨勢預測結果：")
                println("="^80)
                println("號碼  當前Skip  預測Skip  趨勢方向  信心度  異常分數  週期長度")
                println("-"^80)

                # 顯示前20個最有信心的預測
                for trend in trends[1:min(20, length(trends))]
                    cycle_str = trend.cycle_length === nothing ? "無" : string(trend.cycle_length)
                    println("$(lpad(trend.number, 3))   $(lpad(trend.current_skip, 7))   $(lpad(round(trend.predicted_next_skip, digits=1), 7))   $(rpad(trend.trend_direction, 6))   $(lpad(round(trend.confidence_level * 100, digits=1), 5))%   $(lpad(round(trend.anomaly_score, digits=2), 7))    $(cycle_str)")
                end

                # 分類分析
                println("\n📊 趨勢分類統計：")
                up_trends = [t for t in trends if t.trend_direction == "上升"]
                down_trends = [t for t in trends if t.trend_direction == "下降"]
                stable_trends = [t for t in trends if t.trend_direction == "穩定"]

                println("  上升趨勢：$(length(up_trends)) 個號碼")
                println("  下降趨勢：$(length(down_trends)) 個號碼")
                println("  穩定趨勢：$(length(stable_trends)) 個號碼")

                # 高信心度預測
                high_confidence = [t for t in trends if t.confidence_level > 0.7]
                if !isempty(high_confidence)
                    println("\n🎯 高信心度預測 (信心度>70%)：")
                    for trend in high_confidence[1:min(10, length(high_confidence))]
                        println("  號碼 $(trend.number)：$(trend.trend_direction)趨勢，預測Skip=$(round(trend.predicted_next_skip, digits=1))")
                    end
                end

                # 異常檢測
                high_anomaly = [t for t in trends if t.anomaly_score > 0.8]
                if !isempty(high_anomaly)
                    println("\n⚠️  異常號碼檢測 (異常分數>0.8)：")
                    for trend in high_anomaly
                        println("  號碼 $(trend.number)：當前Skip=$(trend.current_skip)，異常分數=$(round(trend.anomaly_score, digits=2))")
                    end
                end

            else
                println("❌ 趨勢預測分析失敗")
            end
        catch e
            println("❌ 趨勢預測分析失敗：$e")
        end

    catch
        println("❌ 無效的期數輸入")
    end
end

"""
選號策略優化分析
"""
function strategy_optimization_analysis(historical_data::Vector{LotteryDraw})
    println("\n🎯 選號策略優化分析")
    println("-"^25)

    print("請選擇策略類型：\n")
    print("1. 激進策略 (高風險高回報)\n")
    print("2. 保守策略 (低風險穩定回報)\n")
    print("3. 平衡策略 (中等風險回報)\n")
    print("請選擇 (1-3): ")

    strategy_choice = strip(readline())

    strategy_type = if strategy_choice == "1"
        "aggressive"
    elseif strategy_choice == "2"
        "conservative"
    elseif strategy_choice == "3"
        "balanced"
    else
        println("❌ 無效選擇，使用平衡策略")
        "balanced"
    end

    try
        # 執行策略優化
        strategy = optimize_selection_strategy(historical_data, strategy_type)

        println("\n📊 策略優化結果：")
        println("="^60)
        println("策略類型：$(strategy["strategy_type"])")
        println("策略描述：$(strategy["description"])")
        println("風險等級：$(strategy["risk_level"])")
        println("預期命中率：$(strategy["expected_hit_rate"])")
        println("投注建議：$(strategy["recommendation"])")

        println("\n🎯 推薦號碼：")
        primary_numbers = strategy["primary_numbers"]
        println("主要號碼：$(join(lpad.(primary_numbers, 2), " "))")

        if haskey(strategy, "secondary_numbers")
            secondary_numbers = strategy["secondary_numbers"]
            if !isempty(secondary_numbers)
                println("備選號碼：$(join(lpad.(secondary_numbers[1:min(10, length(secondary_numbers))], 2), " "))")
            end
        end

        if haskey(strategy, "scoring_details")
            println("\n📈 評分詳情 (前10名)：")
            println("號碼  綜合評分")
            println("-"^15)
            for (number, score) in strategy["scoring_details"]
                println("$(lpad(number, 3))   $(lpad(round(score, digits=1), 6))")
            end
        end

        # 提供組合建議
        println("\n💡 投注組合建議：")
        if length(primary_numbers) >= 5
            println("推薦5號組合：$(join(lpad.(primary_numbers[1:5], 2), " "))")
        end
        if length(primary_numbers) >= 7
            println("推薦7號組合：$(join(lpad.(primary_numbers[1:7], 2), " "))")
        end

    catch e
        println("❌ 策略優化分析失敗：$e")
    end
end

"""
交叉驗證分析
"""
function cross_validation_analysis(historical_data::Vector{LotteryDraw})
    println("\n🔬 交叉驗證分析")
    println("-"^20)

    print("請輸入驗證期數 (建議50-200期): ")
    periods_input = strip(readline())

    try
        periods = parse(Int, periods_input)
        if periods < 30
            println("❌ 期數太少，建議至少30期")
            return
        end

        max_periods = min(periods, length(historical_data))
        if max_periods != periods
            println("⚠️  調整驗證期數為 $(max_periods) 期")
        end

        # 執行交叉驗證
        try
            validation_results = cross_validate_analysis(historical_data, max_periods)

            println("\n📊 交叉驗證結果：")
            println("="^60)

            # Skip預測準確性
            if haskey(validation_results, "skip_prediction_accuracy")
                skip_acc = validation_results["skip_prediction_accuracy"]
                println("\n🎯 Skip預測準確性：")
                println("  準確率：$(skip_acc["accuracy_rate"])%")
                println("  平均誤差：$(skip_acc["average_error"])")
                println("  總預測數：$(skip_acc["total_predictions"])")
                println("  正確預測：$(skip_acc["correct_predictions"])")
            end

            # 趨勢預測驗證
            if haskey(validation_results, "trend_prediction_accuracy")
                trend_acc = validation_results["trend_prediction_accuracy"]
                println("\n📈 趨勢預測準確性：")
                println("  趨勢準確率：$(trend_acc["trend_accuracy_rate"])%")
                println("  正確趨勢：$(trend_acc["correct_trends"])")
                println("  總趨勢數：$(trend_acc["total_trends"])")
            end

            # 策略效果驗證
            if haskey(validation_results, "strategy_performance")
                strategy_perf = validation_results["strategy_performance"]
                println("\n🎯 策略效果驗證：")

                for (strategy_name, performance) in strategy_perf
                    println("  $(strategy_name)策略：")
                    println("    命中率：$(performance["hit_rate"])%")
                    println("    命中次數：$(performance["hits"])/$(performance["total_draws"])")
                    println("    推薦號碼：$(join(lpad.(performance["recommended_numbers"], 2), " "))")
                end
            end

            # 與傳統方法比較
            if haskey(validation_results, "traditional_comparison")
                comparison = validation_results["traditional_comparison"]
                println("\n🔄 與傳統方法比較：")

                freq_analysis = comparison["frequency_analysis"]
                skip_analysis = comparison["skip_analysis"]

                println("  $(freq_analysis["method"])：")
                println("    命中率：$(round(freq_analysis["hit_rate"] * 100, digits=1))%")
                println("    描述：$(freq_analysis["description"])")

                println("  $(skip_analysis["method"])：")
                println("    命中率：$(round(skip_analysis["hit_rate"] * 100, digits=1))%")
                println("    描述：$(skip_analysis["description"])")

                improvement = comparison["improvement"]
                if improvement > 0
                    println("  ✅ Skip分析比傳統方法提升 $(improvement)%")
                elseif improvement < 0
                    println("  ⚠️  Skip分析比傳統方法降低 $(abs(improvement))%")
                else
                    println("  ➡️  兩種方法效果相當")
                end
            end

            println("\n💡 驗證結論：")
            println("  - 交叉驗證有助於評估分析方法的可靠性")
            println("  - 建議結合多種分析方法提高預測準確性")
            println("  - 定期更新模型以適應數據變化")
        catch e
            println("❌ 交叉驗證分析失敗：$e")
        end

    catch
        println("❌ 無效的期數輸入")
    end
end

# 主程式入口
if abspath(PROGRAM_FILE) == @__FILE__
    main_menu()
end
