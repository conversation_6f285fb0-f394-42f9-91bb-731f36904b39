#!/usr/bin/env julia

"""
AmiBroker 圖表模擬器
使用 data_amibroker/fan5/ 中的價格數據繪製週線蠟燭圖
"""

using CSV
using DataFrames
using Dates
using Statistics
using Printf

"""
OHLCV 數據結構
"""
struct OHLCVData
    date::Date
    open::Float64
    high::Float64
    low::Float64
    close::Float64
    volume::Int64
end

"""
從 CSV 文件載入價格數據
"""
function load_price_data(file_path::String)::Vector{OHLCVData}
    if !isfile(file_path)
        throw(ArgumentError("文件不存在：$file_path"))
    end
    
    try
        df = CSV.read(file_path, DataFrame)
        
        # 檢查必要的欄位
        required_columns = ["ticker", "date", "closes", "volume"]
        for col in required_columns
            if !(col in names(df))
                throw(ArgumentError("缺少必要欄位：$col"))
            end
        end
        
        # 轉換數據
        ohlcv_data = OHLCVData[]
        
        for row in eachrow(df)
            date = Date(row.date)
            close_price = Float64(row.closes)
            volume = Int64(row.volume)
            
            # 由於只有收盤價，我們需要模擬 OHLC
            # 使用簡單的方法：假設開盤價等於前一日收盤價
            # 高低價在收盤價附近波動
            open_price = close_price
            high_price = close_price * (1 + abs(randn()) * 0.02)  # 最多2%波動
            low_price = close_price * (1 - abs(randn()) * 0.02)   # 最多2%波動
            
            # 確保 high >= close >= low 和 high >= open >= low
            high_price = max(high_price, close_price, open_price)
            low_price = min(low_price, close_price, open_price)
            
            push!(ohlcv_data, OHLCVData(date, open_price, high_price, low_price, close_price, volume))
        end
        
        # 按日期排序
        sort!(ohlcv_data, by = x -> x.date)
        
        return ohlcv_data
        
    catch e
        throw(ArgumentError("載入數據失敗：$e"))
    end
end

"""
將日線數據轉換為週線數據
"""
function convert_to_weekly(daily_data::Vector{OHLCVData})::Vector{OHLCVData}
    if isempty(daily_data)
        return OHLCVData[]
    end
    
    weekly_data = OHLCVData[]
    current_week_data = OHLCVData[]
    
    for data in daily_data
        # 獲取該日期所在週的週一
        week_start = data.date - Day(dayofweek(data.date) - 1)
        
        if isempty(current_week_data)
            push!(current_week_data, data)
        else
            # 檢查是否還在同一週
            current_week_start = current_week_data[1].date - Day(dayofweek(current_week_data[1].date) - 1)
            
            if week_start == current_week_start
                push!(current_week_data, data)
            else
                # 處理上一週的數據
                if !isempty(current_week_data)
                    weekly_ohlcv = aggregate_week_data(current_week_data)
                    push!(weekly_data, weekly_ohlcv)
                end
                
                # 開始新的一週
                current_week_data = [data]
            end
        end
    end
    
    # 處理最後一週的數據
    if !isempty(current_week_data)
        weekly_ohlcv = aggregate_week_data(current_week_data)
        push!(weekly_data, weekly_ohlcv)
    end
    
    return weekly_data
end

"""
聚合一週的數據為週線 OHLCV
"""
function aggregate_week_data(week_data::Vector{OHLCVData})::OHLCVData
    if isempty(week_data)
        throw(ArgumentError("週數據不能為空"))
    end
    
    # 週線數據規則：
    # Open: 週一開盤價
    # High: 週內最高價
    # Low: 週內最低價
    # Close: 週五收盤價（或最後一個交易日）
    # Volume: 週內成交量總和
    
    open_price = week_data[1].open
    close_price = week_data[end].close
    high_price = maximum(d.high for d in week_data)
    low_price = minimum(d.low for d in week_data)
    total_volume = sum(d.volume for d in week_data)
    
    # 使用週五的日期（或最後一個交易日）
    week_date = week_data[end].date
    
    return OHLCVData(week_date, open_price, high_price, low_price, close_price, total_volume)
end

"""
以文字形式顯示蠟燭圖數據
"""
function display_candlestick_data(ohlcv_data::Vector{OHLCVData}, title::String="蠟燭圖", max_candles::Int=20)
    if isempty(ohlcv_data)
        throw(ArgumentError("數據不能為空"))
    end

    # 限制顯示的蠟燭數量
    data_to_plot = if length(ohlcv_data) > max_candles
        ohlcv_data[end-max_candles+1:end]
    else
        ohlcv_data
    end

    println("\n📊 $title")
    println("="^80)
    println("日期         開盤價    最高價    最低價    收盤價    漲跌      成交量")
    println("-"^80)

    for (i, data) in enumerate(data_to_plot)
        # 計算漲跌
        change_symbol = if i > 1
            prev_close = data_to_plot[i-1].close
            change = data.close - prev_close
            change_pct = (change / prev_close) * 100
            if change > 0
                @sprintf("↗ +%.2f%%", change_pct)
            elseif change < 0
                @sprintf("↘ %.2f%%", change_pct)
            else
                "→ 0.00%"
            end
        else
            "→ --"
        end

        # 格式化輸出
        @printf("%-12s %8.4f  %8.4f  %8.4f  %8.4f  %-10s %8d\n",
                string(data.date),
                data.open,
                data.high,
                data.low,
                data.close,
                change_symbol,
                data.volume)
    end

    println("-"^80)
end

"""
繪製簡單的ASCII圖表
"""
function plot_ascii_chart(ohlcv_data::Vector{OHLCVData}, title::String="價格走勢", width::Int=60, height::Int=20)
    if isempty(ohlcv_data)
        return
    end

    closes = [d.close for d in ohlcv_data]
    dates = [d.date for d in ohlcv_data]

    # 取最近的數據點
    max_points = min(width, length(closes))
    recent_closes = closes[end-max_points+1:end]
    recent_dates = dates[end-max_points+1:end]

    # 計算價格範圍
    min_price = minimum(recent_closes)
    max_price = maximum(recent_closes)
    price_range = max_price - min_price

    if price_range == 0
        price_range = 1.0
    end

    println("\n📈 $title (最近 $max_points 個數據點)")
    println("="^(width + 20))

    # 繪製圖表
    for row in height:-1:1
        # 計算當前行對應的價格
        current_price = min_price + (row - 1) * price_range / (height - 1)

        @printf("%8.4f │", current_price)

        for col in 1:max_points
            price = recent_closes[col]

            # 判斷是否在當前行顯示點
            if abs(price - current_price) <= price_range / (height - 1) / 2
                if col > 1
                    prev_price = recent_closes[col-1]
                    if price > prev_price
                        print("▲")  # 上漲
                    elseif price < prev_price
                        print("▼")  # 下跌
                    else
                        print("■")  # 持平
                    end
                else
                    print("■")
                end
            else
                print(" ")
            end
        end
        println()
    end

    # 繪製底部軸線
    print("         └")
    for i in 1:max_points
        print("─")
    end
    println()

    # 顯示日期範圍
    println("         $(recent_dates[1]) 到 $(recent_dates[end])")
    println("         最低: $(round(min_price, digits=4))  最高: $(round(max_price, digits=4))")
end

"""
獲取可用的股票代碼列表
"""
function get_available_tickers()::Vector{String}
    base_dir = "data_amibroker/fan5/g5"
    
    if !isdir(base_dir)
        println("❌ 數據目錄不存在：$base_dir")
        return String[]
    end
    
    files = readdir(base_dir)
    csv_files = filter(f -> endswith(f, ".csv"), files)
    
    # 提取股票代碼（去掉 .csv 擴展名）
    tickers = [replace(f, ".csv" => "") for f in csv_files]
    
    return sort(tickers)
end

"""
主要的圖表顯示函數
"""
function show_chart(ticker::String="fan5_01", chart_type::String="weekly")
    println("📊 AmiBroker 圖表模擬器")
    println("="^40)
    
    # 構建文件路徑
    file_path = "data_amibroker/fan5/g5/$(ticker).csv"
    
    if !isfile(file_path)
        println("❌ 找不到數據文件：$file_path")
        
        # 顯示可用的股票代碼
        available_tickers = get_available_tickers()
        if !isempty(available_tickers)
            println("\n📋 可用的股票代碼：")
            for (i, t) in enumerate(available_tickers[1:min(10, length(available_tickers))])
                println("  $i. $t")
            end
            if length(available_tickers) > 10
                println("  ... 還有 $(length(available_tickers) - 10) 個")
            end
        end
        return
    end
    
    try
        # 載入數據
        println("📈 載入數據：$ticker")
        daily_data = load_price_data(file_path)
        
        if isempty(daily_data)
            println("❌ 數據為空")
            return
        end
        
        println("✅ 載入 $(length(daily_data)) 天的數據")
        println("📅 數據範圍：$(daily_data[1].date) 到 $(daily_data[end].date)")
        
        # 根據圖表類型處理數據
        if chart_type == "weekly"
            println("🔄 轉換為週線數據...")
            weekly_data = convert_to_weekly(daily_data)
            println("✅ 生成 $(length(weekly_data)) 週的數據")
            
            # 顯示週線圖
            chart_title = "$ticker 週線蠟燭圖"
            display_candlestick_data(weekly_data, chart_title, 20)  # 顯示最近20週
            plot_ascii_chart(weekly_data, "$ticker 週線價格走勢", 60, 15)

        else  # daily
            println("📊 使用日線數據...")

            # 顯示日線圖
            chart_title = "$ticker 日線蠟燭圖"
            display_candlestick_data(daily_data, chart_title, 20)  # 顯示最近20天
            plot_ascii_chart(daily_data, "$ticker 日線價格走勢", 60, 15)
        end
        
        # 顯示統計信息
        data_to_analyze = chart_type == "weekly" ? convert_to_weekly(daily_data) : daily_data
        show_statistics(data_to_analyze, ticker, chart_type)
        
    catch e
        println("❌ 處理數據時發生錯誤：$e")
    end
end

"""
顯示統計信息
"""
function show_statistics(data::Vector{OHLCVData}, ticker::String, chart_type::String)
    if isempty(data)
        return
    end
    
    closes = [d.close for d in data]
    volumes = [d.volume for d in data]
    
    println("\n📊 $ticker $chart_type 統計信息：")
    println("-"^30)
    println("數據期間：$(data[1].date) 到 $(data[end].date)")
    println("總期數：$(length(data))")
    println("最新價格：$(round(closes[end], digits=4))")
    println("最高價格：$(round(maximum(closes), digits=4))")
    println("最低價格：$(round(minimum(closes), digits=4))")
    println("平均價格：$(round(mean(closes), digits=4))")
    println("價格標準差：$(round(std(closes), digits=4))")
    println("平均成交量：$(round(mean(volumes), digits=0))")
    
    # 計算價格變化
    if length(closes) > 1
        price_change = closes[end] - closes[1]
        price_change_pct = (price_change / closes[1]) * 100
        println("總變化：$(round(price_change, digits=4)) ($(round(price_change_pct, digits=2))%)")
    end
end

"""
互動式選單
"""
function interactive_chart_menu()
    while true
        println("\n📊 AmiBroker 圖表模擬器選單")
        println("-"^30)
        println("1. 顯示週線圖")
        println("2. 顯示日線圖")
        println("3. 選擇其他股票")
        println("4. 查看可用股票列表")
        println("5. 退出")
        
        print("\n請選擇 (1-5): ")
        choice = strip(readline())
        
        if choice == "1"
            print("請輸入股票代碼 (預設 fan5_01): ")
            ticker_input = strip(readline())
            ticker = isempty(ticker_input) ? "fan5_01" : String(ticker_input)
            show_chart(ticker, "weekly")
            
        elseif choice == "2"
            print("請輸入股票代碼 (預設 fan5_01): ")
            ticker_input = strip(readline())
            ticker = isempty(ticker_input) ? "fan5_01" : String(ticker_input)
            show_chart(ticker, "daily")
            
        elseif choice == "3"
            available_tickers = get_available_tickers()
            if !isempty(available_tickers)
                println("\n📋 可用的股票代碼：")
                for (i, ticker) in enumerate(available_tickers[1:min(20, length(available_tickers))])
                    println("$(lpad(i, 2)). $ticker")
                end
                if length(available_tickers) > 20
                    println("... 還有 $(length(available_tickers) - 20) 個")
                end
                
                print("\n請輸入股票代碼: ")
                ticker_input = strip(readline())
                if !isempty(ticker_input)
                    ticker = String(ticker_input)
                    print("選擇圖表類型 (weekly/daily, 預設 weekly): ")
                    chart_type_input = strip(readline())
                    chart_type = isempty(chart_type_input) ? "weekly" : String(chart_type_input)
                    show_chart(ticker, chart_type)
                end
            else
                println("❌ 找不到可用的股票數據")
            end
            
        elseif choice == "4"
            available_tickers = get_available_tickers()
            if !isempty(available_tickers)
                println("\n📋 所有可用的股票代碼 ($(length(available_tickers)) 個)：")
                for (i, ticker) in enumerate(available_tickers)
                    print("$(lpad(ticker, 10)) ")
                    if i % 5 == 0
                        println()
                    end
                end
                println()
            else
                println("❌ 找不到可用的股票數據")
            end
            
        elseif choice == "5"
            println("👋 再見！")
            break
            
        else
            println("❌ 無效選擇，請輸入 1-5")
        end
    end
end

# 主程序入口
if abspath(PROGRAM_FILE) == @__FILE__
    # 檢查是否有命令行參數
    if length(ARGS) >= 1
        ticker = ARGS[1]
        chart_type = length(ARGS) >= 2 ? ARGS[2] : "weekly"
        show_chart(ticker, chart_type)
    else
        interactive_chart_menu()
    end
end
