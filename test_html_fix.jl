#!/usr/bin/env julia

"""
測試HTML中文顯示修復功能
"""

println("🧪 測試HTML中文顯示修復功能...")

# 載入修復後的圖表模組
include("src/amibroker_chart_simple.jl")

println("\n📊 當前配置：")
println("後端：$CURRENT_BACKEND")
println("Plots 可用性：$PLOTS_AVAILABLE")

if PLOTS_AVAILABLE
    println("\n📈 生成測試圖表...")
    
    try
        # 生成測試圖表
        show_simple_enhanced_chart("fan5_01", "weekly", "candlestick")
        
        # 檢查最新生成的圖表文件
        charts_dir = "charts"
        if isdir(charts_dir)
            files = readdir(charts_dir)
            html_files = filter(f -> endswith(f, ".html"), files)
            if !isempty(html_files)
                # 按修改時間排序，獲取最新文件
                file_paths = [joinpath(charts_dir, f) for f in html_files]
                latest_file_path = sort(file_paths, by=mtime)[end]
                latest_file = basename(latest_file_path)
                
                println("\n📄 檢查最新圖表文件：$latest_file")
                
                # 檢查HTML結構
                content = read(latest_file_path, String)
                
                println("\n🔍 HTML結構檢查：")
                
                # 檢查字符編碼
                if occursin("charset=UTF-8", content)
                    println("✅ UTF-8 字符編碼：已設置")
                else
                    println("❌ UTF-8 字符編碼：未設置")
                end
                
                # 檢查HTML結構
                if occursin("<!DOCTYPE html>", content)
                    println("✅ HTML5 文檔類型：已設置")
                else
                    println("❌ HTML5 文檔類型：未設置")
                end
                
                # 檢查語言設置
                if occursin("lang=\"zh-TW\"", content)
                    println("✅ 中文語言設置：已設置")
                else
                    println("❌ 中文語言設置：未設置")
                end
                
                # 檢查中文字體設置
                if occursin("Microsoft YaHei", content)
                    println("✅ 中文字體設置：已設置")
                else
                    println("❌ 中文字體設置：未設置")
                end
                
                # 檢查中文字符
                chinese_chars = ["週線", "蠟燭圖", "成交量", "日期", "價格"]
                found_chars = []
                missing_chars = []
                
                for char in chinese_chars
                    if occursin(char, content)
                        push!(found_chars, char)
                    else
                        push!(missing_chars, char)
                    end
                end
                
                println("\n📝 中文字符檢查：")
                println("✅ 找到中文字符：$(join(found_chars, ", "))")
                
                if !isempty(missing_chars)
                    println("⚠️  缺失中文字符：$(join(missing_chars, ", "))")
                end
                
                # 計算修復成功率
                success_rate = length(found_chars) / length(chinese_chars) * 100
                println("\n📊 中文顯示成功率：$(round(success_rate, digits=1))%")
                
                # 檢查文件大小
                file_size = filesize(latest_file_path)
                println("📁 文件大小：$(round(file_size/1024, digits=1)) KB")
                
                # 顯示HTML頭部信息
                println("\n📋 HTML頭部信息：")
                lines = split(content, '\n')
                for (i, line) in enumerate(lines[1:min(20, length(lines))])
                    if occursin("charset", line) || occursin("lang=", line) || 
                       occursin("font-family", line) || occursin("DOCTYPE", line)
                        println("  $(i): $(strip(line))")
                    end
                end
                
                if success_rate >= 90
                    println("\n🎉 HTML中文顯示修復成功！")
                    println("📱 建議使用現代瀏覽器（Chrome、Firefox、Edge）查看")
                elseif success_rate >= 70
                    println("\n✅ HTML中文顯示基本正常")
                    println("💡 如果仍有顯示問題，請檢查瀏覽器字體設置")
                else
                    println("\n⚠️  HTML中文顯示仍有問題")
                    println("🔧 建議檢查系統中文字體安裝")
                end
                
                # 打開文件供查看
                println("\n🌐 正在打開修復後的圖表文件...")
                if Sys.iswindows()
                    try
                        run(`cmd /c start $latest_file_path`)
                        println("✅ 圖表已在默認瀏覽器中打開")
                    catch
                        println("⚠️  無法自動打開瀏覽器，請手動打開：$latest_file_path")
                    end
                end
                
            else
                println("❌ 未找到HTML圖表文件")
            end
        else
            println("❌ charts目錄不存在")
        end
        
    catch e
        println("❌ 測試失敗：$e")
        println("詳細錯誤：")
        showerror(stdout, e, catch_backtrace())
    end
    
else
    println("❌ Plots.jl 不可用，無法測試")
end

println("\n💡 修復說明：")
println("1. 添加了完整的HTML5結構")
println("2. 設置了UTF-8字符編碼")
println("3. 指定了中文語言環境")
println("4. 配置了中文字體優先級")
println("5. 添加了響應式樣式")

println("\n🎯 如果瀏覽器中仍有中文顯示問題：")
println("1. 確保瀏覽器支持UTF-8編碼")
println("2. 檢查系統是否安裝了微軟雅黑字體")
println("3. 嘗試使用不同的瀏覽器")
println("4. 清除瀏覽器緩存後重新打開")

println("\n🎉 測試完成！")
