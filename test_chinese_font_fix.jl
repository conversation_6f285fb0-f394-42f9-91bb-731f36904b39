#!/usr/bin/env julia

"""
測試中文字體修復效果
"""

println("🧪 測試中文字體修復...")

# 載入修復後的圖表模組
include("src/amibroker_chart_simple.jl")

println("\n📊 當前後端：$CURRENT_BACKEND")
println("Plots 可用性：$PLOTS_AVAILABLE")

if PLOTS_AVAILABLE
    println("\n🔧 測試字體設置...")
    
    # 測試字體設置函數
    font_settings = get_font_settings()
    println("字體設置：")
    for (key, value) in font_settings
        println("  $key: $value")
    end
    
    # 設置中文字體
    setup_chinese_fonts()
    
    println("\n📈 生成測試圖表...")
    
    try
        # 測試生成圖表
        show_simple_enhanced_chart("fan5_01", "weekly", "candlestick")
        println("✅ 圖表生成成功")
        
        # 檢查最新生成的圖表文件
        charts_dir = "charts"
        if isdir(charts_dir)
            files = readdir(charts_dir)
            html_files = filter(f -> endswith(f, ".html"), files)
            if !isempty(html_files)
                latest_file = sort(html_files)[end]
                println("📄 最新圖表文件：$latest_file")
                
                # 檢查文件中的中文字符
                file_path = joinpath(charts_dir, latest_file)
                content = read(file_path, String)
                
                chinese_chars = ["週線", "蠟燭圖", "成交量", "日期", "價格", "上漲", "下跌"]
                found_chars = []
                
                for char in chinese_chars
                    if occursin(char, content)
                        push!(found_chars, char)
                    end
                end
                
                println("✅ 找到中文字符：$(join(found_chars, ", "))")
                
                if length(found_chars) == length(chinese_chars)
                    println("🎉 中文字符完整保存")
                else
                    missing_chars = setdiff(chinese_chars, found_chars)
                    println("⚠️  缺失中文字符：$(join(missing_chars, ", "))")
                end
            end
        end
        
    catch e
        println("❌ 圖表生成失敗：$e")
        println("詳細錯誤：")
        showerror(stdout, e, catch_backtrace())
    end
    
else
    println("❌ Plots.jl 不可用，無法測試圖表功能")
end

println("\n🎯 修復建議：")
println("1. 如果使用 GR 後端，建議安裝 PlotlyJS 以獲得更好的中文支持")
println("2. 確保系統安裝了中文字體（如微軟雅黑、SimHei等）")
println("3. 使用 PlotlyJS 後端可以獲得最佳的中文顯示效果")

println("\n📦 安裝 PlotlyJS 命令：")
println("julia --project=. -e \"using Pkg; Pkg.add(\\\"PlotlyJS\\\")\"")

println("\n🎉 測試完成")
