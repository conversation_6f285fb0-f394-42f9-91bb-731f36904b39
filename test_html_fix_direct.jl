#!/usr/bin/env julia

"""
直接測試HTML修復功能
"""

println("🧪 直接測試HTML修復功能...")

# 載入修復後的圖表模組
include("src/amibroker_chart_simple.jl")

println("\n📊 當前配置：")
println("後端：$CURRENT_BACKEND")
println("Plots 可用性：$PLOTS_AVAILABLE")

if PLOTS_AVAILABLE
    println("\n📈 使用有效股票代碼生成圖表...")
    
    try
        # 使用一個已知存在的股票代碼
        valid_ticker = "fan5_01,02,07,09,22"
        println("📊 使用股票代碼：$valid_ticker")
        
        # 生成圖表
        show_simple_enhanced_chart(valid_ticker, "weekly", "candlestick")
        
        # 檢查最新生成的圖表文件
        charts_dir = "charts"
        if isdir(charts_dir)
            files = readdir(charts_dir)
            html_files = filter(f -> endswith(f, ".html"), files)
            if !isempty(html_files)
                # 按修改時間排序，獲取最新文件
                file_paths = [joinpath(charts_dir, f) for f in html_files]
                latest_file_path = sort(file_paths, by=mtime)[end]
                latest_file = basename(latest_file_path)
                
                println("\n📄 檢查最新圖表文件：$latest_file")
                
                # 檢查HTML結構
                content = read(latest_file_path, String)
                
                println("\n🔍 HTML結構檢查：")
                
                # 檢查字符編碼
                if occursin("charset=UTF-8", content)
                    println("✅ UTF-8 字符編碼：已設置")
                else
                    println("❌ UTF-8 字符編碼：未設置")
                end
                
                # 檢查HTML結構
                if occursin("<!DOCTYPE html>", content)
                    println("✅ HTML5 文檔類型：已設置")
                else
                    println("❌ HTML5 文檔類型：未設置")
                end
                
                # 檢查語言設置
                if occursin("lang=\"zh-TW\"", content)
                    println("✅ 中文語言設置：已設置")
                else
                    println("❌ 中文語言設置：未設置")
                end
                
                # 檢查CSS樣式
                if occursin("font-family:", content) && occursin("Microsoft YaHei", content)
                    println("✅ CSS中文字體：已設置")
                else
                    println("❌ CSS中文字體：未設置")
                end
                
                # 檢查圖表容器
                if occursin("chart-container", content)
                    println("✅ 圖表容器樣式：已設置")
                else
                    println("❌ 圖表容器樣式：未設置")
                end
                
                # 檢查中文字符
                chinese_chars = ["週線", "蠟燭圖", "成交量", "日期", "價格"]
                found_chars = []
                
                for char in chinese_chars
                    if occursin(char, content)
                        push!(found_chars, char)
                    end
                end
                
                println("\n📝 中文字符檢查：")
                println("✅ 找到中文字符：$(join(found_chars, ", "))")
                
                # 計算修復成功率
                success_rate = length(found_chars) / length(chinese_chars) * 100
                println("\n📊 中文顯示成功率：$(round(success_rate, digits=1))%")
                
                # 顯示HTML頭部（前30行）
                println("\n📋 HTML頭部結構：")
                lines = split(content, '\n')
                for (i, line) in enumerate(lines[1:min(30, length(lines))])
                    if !isempty(strip(line))
                        println("  $(i): $(strip(line))")
                    end
                end
                
                if occursin("<!DOCTYPE html>", content) && occursin("charset=UTF-8", content)
                    println("\n🎉 HTML修復成功！文件已包含完整的HTML5結構和UTF-8編碼")
                else
                    println("\n⚠️  HTML修復可能未完全生效")
                end
                
                # 打開文件供查看
                println("\n🌐 正在打開修復後的圖表文件...")
                if Sys.iswindows()
                    try
                        run(`cmd /c start $latest_file_path`)
                        println("✅ 圖表已在默認瀏覽器中打開")
                    catch
                        println("⚠️  無法自動打開瀏覽器，請手動打開：$latest_file_path")
                    end
                end
                
            else
                println("❌ 未找到HTML圖表文件")
            end
        else
            println("❌ charts目錄不存在")
        end
        
    catch e
        println("❌ 測試失敗：$e")
        println("詳細錯誤：")
        showerror(stdout, e, catch_backtrace())
    end
    
else
    println("❌ Plots.jl 不可用，無法測試")
end

println("\n🎉 測試完成！")
