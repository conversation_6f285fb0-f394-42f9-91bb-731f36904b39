#!/usr/bin/env julia

"""
測試最終的中文字體修復效果
"""

println("🧪 測試最終中文字體修復效果...")

# 載入修復後的圖表模組
include("src/amibroker_chart_simple.jl")

println("\n📊 當前配置：")
println("後端：$CURRENT_BACKEND")
println("Plots 可用性：$PLOTS_AVAILABLE")

if PLOTS_AVAILABLE
    println("\n🔧 測試字體設置...")
    
    # 測試字體設置函數
    font_settings = get_font_settings()
    println("字體設置：")
    for (key, value) in font_settings
        println("  $key: $value")
    end
    
    println("\n📈 生成測試圖表（自動保存）...")
    
    try
        # 測試生成圖表（週線蠟燭圖）
        show_simple_enhanced_chart("fan5_01", "weekly", "candlestick")
        
        # 檢查最新生成的圖表文件
        charts_dir = "charts"
        if isdir(charts_dir)
            files = readdir(charts_dir)
            html_files = filter(f -> endswith(f, ".html"), files)
            if !isempty(html_files)
                # 按修改時間排序，獲取最新文件
                file_paths = [joinpath(charts_dir, f) for f in html_files]
                latest_file_path = sort(file_paths, by=mtime)[end]
                latest_file = basename(latest_file_path)
                
                println("\n📄 最新圖表文件：$latest_file")
                
                # 檢查文件中的中文字符
                content = read(latest_file_path, String)
                
                chinese_chars = ["週線", "蠟燭圖", "成交量", "日期", "價格", "上漲", "下跌"]
                found_chars = []
                missing_chars = []
                
                for char in chinese_chars
                    if occursin(char, content)
                        push!(found_chars, char)
                    else
                        push!(missing_chars, char)
                    end
                end
                
                println("✅ 找到中文字符：$(join(found_chars, ", "))")
                
                if !isempty(missing_chars)
                    println("⚠️  缺失中文字符：$(join(missing_chars, ", "))")
                end
                
                # 計算修復成功率
                success_rate = length(found_chars) / length(chinese_chars) * 100
                println("📊 中文顯示成功率：$(round(success_rate, digits=1))%")
                
                if success_rate >= 80
                    println("🎉 中文顯示修復成功！")
                elseif success_rate >= 60
                    println("✅ 中文顯示基本正常，仍有改進空間")
                else
                    println("⚠️  中文顯示仍有問題")
                end
                
                # 打開圖表文件供用戶查看
                println("\n🌐 正在打開圖表文件供查看...")
                if Sys.iswindows()
                    run(`cmd /c start $latest_file_path`)
                elseif Sys.islinux()
                    run(`xdg-open $latest_file_path`)
                elseif Sys.isapple()
                    run(`open $latest_file_path`)
                end
            end
        end
        
    catch e
        println("❌ 圖表生成失敗：$e")
        println("詳細錯誤：")
        showerror(stdout, e, catch_backtrace())
    end
    
else
    println("❌ Plots.jl 不可用，無法測試圖表功能")
end

println("\n💡 修復總結：")
if CURRENT_BACKEND == "plotlyjs"
    println("✅ 使用 PlotlyJS 後端，中文支持最佳")
elseif CURRENT_BACKEND == "gr"
    println("⚠️  使用 GR 後端，中文支持有限")
    println("   建議安裝 PlotlyJS：julia --project=. -e \"using Pkg; Pkg.add(\\\"PlotlyJS\\\")\"")
else
    println("⚠️  使用默認後端，中文支持有限")
end

println("\n🎯 使用建議：")
println("1. 確保使用 PlotlyJS 後端以獲得最佳中文顯示效果")
println("2. 圖表會自動保存為 HTML 格式，支持交互式查看")
println("3. 如果仍有中文顯示問題，請檢查系統是否安裝了中文字體")

println("\n🎉 測試完成！")
