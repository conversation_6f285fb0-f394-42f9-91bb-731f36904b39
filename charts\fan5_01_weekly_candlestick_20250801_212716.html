<?xml version="1.0" encoding="utf-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="1200" height="800" viewBox="0 0 4800 3200">
<defs>
  <clipPath id="clip000">
    <rect x="0" y="0" width="4800" height="3200"/>
  </clipPath>
</defs>
<path clip-path="url(#clip000)" d="M0 3200 L4800 3200 L4800 0 L0 0  Z" fill="#ffffff" fill-rule="evenodd" fill-opacity="1"/>
<defs>
  <clipPath id="clip001">
    <rect x="960" y="0" width="3361" height="3200"/>
  </clipPath>
</defs>
<path clip-path="url(#clip000)" d="M277.619 2055.51 L4752.76 2055.51 L4752.76 123.472 L277.619 123.472  Z" fill="#ffffff" fill-rule="evenodd" fill-opacity="1"/>
<defs>
  <clipPath id="clip002">
    <rect x="277" y="123" width="4476" height="1933"/>
  </clipPath>
</defs>
<polyline clip-path="url(#clip002)" style="stroke:#d3d3d3; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.3; fill:none" points="1094.67,2055.51 1094.67,123.472 "/>
<polyline clip-path="url(#clip002)" style="stroke:#d3d3d3; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.3; fill:none" points="3330.84,2055.51 3330.84,123.472 "/>
<polyline clip-path="url(#clip002)" style="stroke:#d3d3d3; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.3; fill:none" points="277.619,1824.36 4752.76,1824.36 "/>
<polyline clip-path="url(#clip002)" style="stroke:#d3d3d3; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.3; fill:none" points="277.619,1309.65 4752.76,1309.65 "/>
<polyline clip-path="url(#clip002)" style="stroke:#d3d3d3; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.3; fill:none" points="277.619,794.936 4752.76,794.936 "/>
<polyline clip-path="url(#clip002)" style="stroke:#d3d3d3; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.3; fill:none" points="277.619,280.224 4752.76,280.224 "/>
<polyline clip-path="url(#clip000)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:1; fill:none" points="277.619,2055.51 4752.76,2055.51 "/>
<polyline clip-path="url(#clip000)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:1; fill:none" points="1094.67,2055.51 1094.67,2036.62 "/>
<polyline clip-path="url(#clip000)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:1; fill:none" points="3330.84,2055.51 3330.84,2036.62 "/>
<path clip-path="url(#clip000)" d="M950.443 2138.09 L968.802 2138.09 L968.802 2142.51 L944.115 2142.51 L944.115 2138.09 Q947.11 2134.99 952.266 2129.78 Q957.448 2124.55 958.776 2123.03 Q961.302 2120.2 962.292 2118.24 Q963.307 2116.26 963.307 2114.36 Q963.307 2111.26 961.12 2109.31 Q958.958 2107.36 955.469 2107.36 Q952.995 2107.36 950.235 2108.22 Q947.5 2109.08 944.375 2110.82 L944.375 2105.51 Q947.552 2104.23 950.313 2103.58 Q953.073 2102.93 955.365 2102.93 Q961.406 2102.93 965 2105.95 Q968.594 2108.97 968.594 2114.02 Q968.594 2116.42 967.682 2118.58 Q966.797 2120.72 964.427 2123.63 Q963.776 2124.39 960.287 2128.01 Q956.797 2131.6 950.443 2138.09 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M991.094 2107.1 Q987.031 2107.1 984.974 2111.11 Q982.943 2115.09 982.943 2123.11 Q982.943 2131.11 984.974 2135.12 Q987.031 2139.1 991.094 2139.1 Q995.182 2139.1 997.214 2135.12 Q999.271 2131.11 999.271 2123.11 Q999.271 2115.09 997.214 2111.11 Q995.182 2107.1 991.094 2107.1 M991.094 2102.93 Q997.63 2102.93 1001.07 2108.11 Q1004.53 2113.27 1004.53 2123.11 Q1004.53 2132.93 1001.07 2138.11 Q997.63 2143.27 991.094 2143.27 Q984.557 2143.27 981.094 2138.11 Q977.656 2132.93 977.656 2123.11 Q977.656 2113.27 981.094 2108.11 Q984.557 2102.93 991.094 2102.93 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M1018.31 2138.09 L1036.67 2138.09 L1036.67 2142.51 L1011.98 2142.51 L1011.98 2138.09 Q1014.97 2134.99 1020.13 2129.78 Q1025.31 2124.55 1026.64 2123.03 Q1029.17 2120.2 1030.16 2118.24 Q1031.17 2116.26 1031.17 2114.36 Q1031.17 2111.26 1028.98 2109.31 Q1026.82 2107.36 1023.33 2107.36 Q1020.86 2107.36 1018.1 2108.22 Q1015.36 2109.08 1012.24 2110.82 L1012.24 2105.51 Q1015.42 2104.23 1018.18 2103.58 Q1020.94 2102.93 1023.23 2102.93 Q1029.27 2102.93 1032.86 2105.95 Q1036.46 2108.97 1036.46 2114.02 Q1036.46 2116.42 1035.55 2118.58 Q1034.66 2120.72 1032.29 2123.63 Q1031.64 2124.39 1028.15 2128.01 Q1024.66 2131.6 1018.31 2138.09 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M1062.16 2108.22 L1048.88 2128.97 L1062.16 2128.97 L1062.16 2108.22 M1060.78 2103.63 L1067.4 2103.63 L1067.4 2128.97 L1072.94 2128.97 L1072.94 2133.35 L1067.4 2133.35 L1067.4 2142.51 L1062.16 2142.51 L1062.16 2133.35 L1044.61 2133.35 L1044.61 2128.27 L1060.78 2103.63 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M1078.54 2125.77 L1092.58 2125.77 L1092.58 2130.04 L1078.54 2130.04 L1078.54 2125.77 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M1112.13 2107.1 Q1108.07 2107.1 1106.02 2111.11 Q1103.98 2115.09 1103.98 2123.11 Q1103.98 2131.11 1106.02 2135.12 Q1108.07 2139.1 1112.13 2139.1 Q1116.22 2139.1 1118.25 2135.12 Q1120.31 2131.11 1120.31 2123.11 Q1120.31 2115.09 1118.25 2111.11 Q1116.22 2107.1 1112.13 2107.1 M1112.13 2102.93 Q1118.67 2102.93 1122.11 2108.11 Q1125.57 2113.27 1125.57 2123.11 Q1125.57 2132.93 1122.11 2138.11 Q1118.67 2143.27 1112.13 2143.27 Q1105.6 2143.27 1102.13 2138.11 Q1098.7 2132.93 1098.7 2123.11 Q1098.7 2113.27 1102.13 2108.11 Q1105.6 2102.93 1112.13 2102.93 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M1135.73 2138.09 L1144.32 2138.09 L1144.32 2108.43 L1134.97 2110.3 L1134.97 2105.51 L1144.27 2103.63 L1149.53 2103.63 L1149.53 2138.09 L1158.12 2138.09 L1158.12 2142.51 L1135.73 2142.51 L1135.73 2138.09 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M1165.65 2125.77 L1179.69 2125.77 L1179.69 2130.04 L1165.65 2130.04 L1165.65 2125.77 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M1199.24 2107.1 Q1195.18 2107.1 1193.12 2111.11 Q1191.09 2115.09 1191.09 2123.11 Q1191.09 2131.11 1193.12 2135.12 Q1195.18 2139.1 1199.24 2139.1 Q1203.33 2139.1 1205.36 2135.12 Q1207.42 2131.11 1207.42 2123.11 Q1207.42 2115.09 1205.36 2111.11 Q1203.33 2107.1 1199.24 2107.1 M1199.24 2102.93 Q1205.78 2102.93 1209.22 2108.11 Q1212.68 2113.27 1212.68 2123.11 Q1212.68 2132.93 1209.22 2138.11 Q1205.78 2143.27 1199.24 2143.27 Q1192.71 2143.27 1189.24 2138.11 Q1185.81 2132.93 1185.81 2123.11 Q1185.81 2113.27 1189.24 2108.11 Q1192.71 2102.93 1199.24 2102.93 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M1222.84 2138.09 L1231.43 2138.09 L1231.43 2108.43 L1222.08 2110.3 L1222.08 2105.51 L1231.38 2103.63 L1236.64 2103.63 L1236.64 2138.09 L1245.23 2138.09 L1245.23 2142.51 L1222.84 2142.51 L1222.84 2138.09 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M3186.61 2138.09 L3204.97 2138.09 L3204.97 2142.51 L3180.28 2142.51 L3180.28 2138.09 Q3183.27 2134.99 3188.43 2129.78 Q3193.61 2124.55 3194.94 2123.03 Q3197.47 2120.2 3198.46 2118.24 Q3199.47 2116.26 3199.47 2114.36 Q3199.47 2111.26 3197.28 2109.31 Q3195.12 2107.36 3191.63 2107.36 Q3189.16 2107.36 3186.4 2108.22 Q3183.66 2109.08 3180.54 2110.82 L3180.54 2105.51 Q3183.72 2104.23 3186.48 2103.58 Q3189.24 2102.93 3191.53 2102.93 Q3197.57 2102.93 3201.16 2105.95 Q3204.76 2108.97 3204.76 2114.02 Q3204.76 2116.42 3203.85 2118.58 Q3202.96 2120.72 3200.59 2123.63 Q3199.94 2124.39 3196.45 2128.01 Q3192.96 2131.6 3186.61 2138.09 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M3227.26 2107.1 Q3223.19 2107.1 3221.14 2111.11 Q3219.11 2115.09 3219.11 2123.11 Q3219.11 2131.11 3221.14 2135.12 Q3223.19 2139.1 3227.26 2139.1 Q3231.35 2139.1 3233.38 2135.12 Q3235.43 2131.11 3235.43 2123.11 Q3235.43 2115.09 3233.38 2111.11 Q3231.35 2107.1 3227.26 2107.1 M3227.26 2102.93 Q3233.79 2102.93 3237.23 2108.11 Q3240.69 2113.27 3240.69 2123.11 Q3240.69 2132.93 3237.23 2138.11 Q3233.79 2143.27 3227.26 2143.27 Q3220.72 2143.27 3217.26 2138.11 Q3213.82 2132.93 3213.82 2123.11 Q3213.82 2113.27 3217.26 2108.11 Q3220.72 2102.93 3227.26 2102.93 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M3254.47 2138.09 L3272.83 2138.09 L3272.83 2142.51 L3248.14 2142.51 L3248.14 2138.09 Q3251.14 2134.99 3256.29 2129.78 Q3261.48 2124.55 3262.8 2123.03 Q3265.33 2120.2 3266.32 2118.24 Q3267.33 2116.26 3267.33 2114.36 Q3267.33 2111.26 3265.15 2109.31 Q3262.99 2107.36 3259.5 2107.36 Q3257.02 2107.36 3254.26 2108.22 Q3251.53 2109.08 3248.4 2110.82 L3248.4 2105.51 Q3251.58 2104.23 3254.34 2103.58 Q3257.1 2102.93 3259.39 2102.93 Q3265.43 2102.93 3269.03 2105.95 Q3272.62 2108.97 3272.62 2114.02 Q3272.62 2116.42 3271.71 2118.58 Q3270.82 2120.72 3268.45 2123.63 Q3267.8 2124.39 3264.31 2128.01 Q3260.82 2131.6 3254.47 2138.09 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M3283.92 2103.63 L3304.57 2103.63 L3304.57 2108.06 L3288.74 2108.06 L3288.74 2117.59 Q3289.89 2117.2 3291.03 2117.02 Q3292.18 2116.81 3293.32 2116.81 Q3299.83 2116.81 3303.64 2120.38 Q3307.44 2123.95 3307.44 2130.04 Q3307.44 2136.32 3303.53 2139.81 Q3299.63 2143.27 3292.52 2143.27 Q3290.07 2143.27 3287.52 2142.85 Q3284.99 2142.44 3282.28 2141.6 L3282.28 2136.32 Q3284.63 2137.59 3287.13 2138.22 Q3289.63 2138.84 3292.41 2138.84 Q3296.92 2138.84 3299.55 2136.47 Q3302.18 2134.1 3302.18 2130.04 Q3302.18 2125.98 3299.55 2123.61 Q3296.92 2121.24 3292.41 2121.24 Q3290.3 2121.24 3288.19 2121.71 Q3286.11 2122.18 3283.92 2123.16 L3283.92 2103.63 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M3314.7 2125.77 L3328.74 2125.77 L3328.74 2130.04 L3314.7 2130.04 L3314.7 2125.77 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M3348.3 2107.1 Q3344.24 2107.1 3342.18 2111.11 Q3340.15 2115.09 3340.15 2123.11 Q3340.15 2131.11 3342.18 2135.12 Q3344.24 2139.1 3348.3 2139.1 Q3352.39 2139.1 3354.42 2135.12 Q3356.48 2131.11 3356.48 2123.11 Q3356.48 2115.09 3354.42 2111.11 Q3352.39 2107.1 3348.3 2107.1 M3348.3 2102.93 Q3354.83 2102.93 3358.27 2108.11 Q3361.74 2113.27 3361.74 2123.11 Q3361.74 2132.93 3358.27 2138.11 Q3354.83 2143.27 3348.3 2143.27 Q3341.76 2143.27 3338.3 2138.11 Q3334.86 2132.93 3334.86 2123.11 Q3334.86 2113.27 3338.3 2108.11 Q3341.76 2102.93 3348.3 2102.93 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M3371.89 2138.09 L3380.49 2138.09 L3380.49 2108.43 L3371.14 2110.3 L3371.14 2105.51 L3380.43 2103.63 L3385.69 2103.63 L3385.69 2138.09 L3394.29 2138.09 L3394.29 2142.51 L3371.89 2142.51 L3371.89 2138.09 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M3401.81 2125.77 L3415.85 2125.77 L3415.85 2130.04 L3401.81 2130.04 L3401.81 2125.77 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M3435.41 2107.1 Q3431.34 2107.1 3429.29 2111.11 Q3427.26 2115.09 3427.26 2123.11 Q3427.26 2131.11 3429.29 2135.12 Q3431.34 2139.1 3435.41 2139.1 Q3439.5 2139.1 3441.53 2135.12 Q3443.58 2131.11 3443.58 2123.11 Q3443.58 2115.09 3441.53 2111.11 Q3439.5 2107.1 3435.41 2107.1 M3435.41 2102.93 Q3441.94 2102.93 3445.38 2108.11 Q3448.84 2113.27 3448.84 2123.11 Q3448.84 2132.93 3445.38 2138.11 Q3441.94 2143.27 3435.41 2143.27 Q3428.87 2143.27 3425.41 2138.11 Q3421.97 2132.93 3421.97 2123.11 Q3421.97 2113.27 3425.41 2108.11 Q3428.87 2102.93 3435.41 2102.93 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M3459 2138.09 L3467.59 2138.09 L3467.59 2108.43 L3458.25 2110.3 L3458.25 2105.51 L3467.54 2103.63 L3472.8 2103.63 L3472.8 2138.09 L3481.4 2138.09 L3481.4 2142.51 L3459 2142.51 L3459 2138.09 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M2479.33 2254.38 L2479.33 2196.9 L2511.93 2196.9 L2511.93 2254.38 L2479.33 2254.38 M2482.99 2250.75 L2508.3 2250.75 L2508.3 2200.56 L2482.99 2200.56 L2482.99 2250.75 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M2518.45 2254.38 L2518.45 2196.9 L2551.04 2196.9 L2551.04 2254.38 L2518.45 2254.38 M2522.11 2250.75 L2547.41 2250.75 L2547.41 2200.56 L2522.11 2200.56 L2522.11 2250.75 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><polyline clip-path="url(#clip000)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:1; fill:none" points="277.619,2055.51 277.619,123.472 "/>
<polyline clip-path="url(#clip000)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:1; fill:none" points="277.619,1824.36 296.517,1824.36 "/>
<polyline clip-path="url(#clip000)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:1; fill:none" points="277.619,1309.65 296.517,1309.65 "/>
<polyline clip-path="url(#clip000)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:1; fill:none" points="277.619,794.936 296.517,794.936 "/>
<polyline clip-path="url(#clip000)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:1; fill:none" points="277.619,280.224 296.517,280.224 "/>
<path clip-path="url(#clip000)" d="M102.26 1824.87 L135.645 1824.87 L135.645 1829.3 L102.26 1829.3 L102.26 1824.87 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M147.911 1839.37 L156.505 1839.37 L156.505 1809.71 L147.156 1811.59 L147.156 1806.8 L156.453 1804.92 L161.713 1804.92 L161.713 1839.37 L170.307 1839.37 L170.307 1843.8 L147.911 1843.8 L147.911 1839.37 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M192.182 1808.38 Q188.119 1808.38 186.062 1812.39 Q184.031 1816.38 184.031 1824.4 Q184.031 1832.39 186.062 1836.4 Q188.119 1840.39 192.182 1840.39 Q196.27 1840.39 198.301 1836.4 Q200.359 1832.39 200.359 1824.4 Q200.359 1816.38 198.301 1812.39 Q196.27 1808.38 192.182 1808.38 M192.182 1804.22 Q198.718 1804.22 202.155 1809.4 Q205.619 1814.56 205.619 1824.4 Q205.619 1834.22 202.155 1839.4 Q198.718 1844.56 192.182 1844.56 Q185.645 1844.56 182.182 1839.4 Q178.744 1834.22 178.744 1824.4 Q178.744 1814.56 182.182 1809.4 Q185.645 1804.22 192.182 1804.22 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M137.312 1310.16 L170.697 1310.16 L170.697 1314.58 L137.312 1314.58 L137.312 1310.16 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M182.103 1290.21 L202.754 1290.21 L202.754 1294.64 L186.921 1294.64 L186.921 1304.17 Q188.067 1303.78 189.213 1303.59 Q190.359 1303.39 191.504 1303.39 Q198.015 1303.39 201.817 1306.95 Q205.619 1310.52 205.619 1316.61 Q205.619 1322.89 201.713 1326.38 Q197.807 1329.84 190.697 1329.84 Q188.249 1329.84 185.697 1329.43 Q183.171 1329.01 180.463 1328.18 L180.463 1322.89 Q182.807 1324.17 185.307 1324.79 Q187.807 1325.42 190.593 1325.42 Q195.098 1325.42 197.728 1323.05 Q200.359 1320.68 200.359 1316.61 Q200.359 1312.55 197.728 1310.18 Q195.098 1307.81 190.593 1307.81 Q188.484 1307.81 186.374 1308.28 Q184.291 1308.75 182.103 1309.74 L182.103 1290.21 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M192.182 778.96 Q188.119 778.96 186.062 782.97 Q184.031 786.955 184.031 794.975 Q184.031 802.97 186.062 806.981 Q188.119 810.965 192.182 810.965 Q196.27 810.965 198.301 806.981 Q200.359 802.97 200.359 794.975 Q200.359 786.955 198.301 782.97 Q196.27 778.96 192.182 778.96 M192.182 774.793 Q198.718 774.793 202.155 779.976 Q205.619 785.132 205.619 794.975 Q205.619 804.793 202.155 809.975 Q198.718 815.132 192.182 815.132 Q185.645 815.132 182.182 809.975 Q178.744 804.793 178.744 794.975 Q178.744 785.132 182.182 779.976 Q185.645 774.793 192.182 774.793 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M182.103 260.784 L202.754 260.784 L202.754 265.211 L186.921 265.211 L186.921 274.743 Q188.067 274.352 189.213 274.17 Q190.359 273.961 191.504 273.961 Q198.015 273.961 201.817 277.529 Q205.619 281.097 205.619 287.19 Q205.619 293.466 201.713 296.956 Q197.807 300.42 190.697 300.42 Q188.249 300.42 185.697 300.003 Q183.171 299.586 180.463 298.753 L180.463 293.466 Q182.807 294.742 185.307 295.367 Q187.807 295.992 190.593 295.992 Q195.098 295.992 197.728 293.623 Q200.359 291.253 200.359 287.19 Q200.359 283.128 197.728 280.758 Q195.098 278.388 190.593 278.388 Q188.484 278.388 186.374 278.857 Q184.291 279.326 182.103 280.315 L182.103 260.784 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M27.5261 1125.35 L-29.9562 1125.35 L-29.9562 1092.76 L27.5261 1092.76 L27.5261 1125.35 M23.8976 1121.69 L23.8976 1096.38 L-26.296 1096.38 L-26.296 1121.69 L23.8976 1121.69 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M27.5261 1086.23 L-29.9562 1086.23 L-29.9562 1053.64 L27.5261 1053.64 L27.5261 1086.23 M23.8976 1082.57 L23.8976 1057.27 L-26.296 1057.27 L-26.296 1082.57 L23.8976 1082.57 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M2230.04 9.54393 L2230.04 15.7418 L2222.91 15.7418 Q2218.9 15.7418 2217.32 17.3622 Q2215.79 18.9825 2215.79 23.1955 L2215.79 27.2059 L2228.06 27.2059 L2228.06 32.9987 L2215.79 32.9987 L2215.79 72.576 L2208.29 72.576 L2208.29 32.9987 L2201.16 32.9987 L2201.16 27.2059 L2208.29 27.2059 L2208.29 24.0462 Q2208.29 16.471 2211.82 13.0277 Q2215.34 9.54393 2223 9.54393 L2230.04 9.54393 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M2256.9 49.7694 Q2247.87 49.7694 2244.38 51.8354 Q2240.9 53.9013 2240.9 58.8839 Q2240.9 62.8538 2243.49 65.2034 Q2246.13 67.5124 2250.62 67.5124 Q2256.82 67.5124 2260.55 63.1374 Q2264.31 58.7219 2264.31 51.4303 L2264.31 49.7694 L2256.9 49.7694 M2271.77 46.6907 L2271.77 72.576 L2264.31 72.576 L2264.31 65.6895 Q2261.76 69.8214 2257.96 71.8063 Q2254.15 73.7508 2248.64 73.7508 Q2241.67 73.7508 2237.54 69.8619 Q2233.45 65.9325 2233.45 59.3701 Q2233.45 51.7138 2238.55 47.825 Q2243.7 43.9361 2253.86 43.9361 L2264.31 43.9361 L2264.31 43.2069 Q2264.31 38.0623 2260.91 35.2672 Q2257.55 32.4315 2251.43 32.4315 Q2247.54 32.4315 2243.86 33.3632 Q2240.17 34.295 2236.77 36.1584 L2236.77 29.2718 Q2240.86 27.692 2244.71 26.9223 Q2248.56 26.1121 2252.2 26.1121 Q2262.05 26.1121 2266.91 31.2163 Q2271.77 36.3204 2271.77 46.6907 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M2324.84 45.1919 L2324.84 72.576 L2317.38 72.576 L2317.38 45.4349 Q2317.38 38.994 2314.87 35.7938 Q2312.36 32.5936 2307.34 32.5936 Q2301.3 32.5936 2297.82 36.4419 Q2294.33 40.2903 2294.33 46.9338 L2294.33 72.576 L2286.84 72.576 L2286.84 27.2059 L2294.33 27.2059 L2294.33 34.2544 Q2297.01 30.163 2300.61 28.1376 Q2304.26 26.1121 2309 26.1121 Q2316.81 26.1121 2320.83 30.9732 Q2324.84 35.7938 2324.84 45.1919 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M2340.84 12.096 L2372.96 12.096 L2372.96 18.9825 L2348.33 18.9825 L2348.33 33.8088 Q2350.11 33.2012 2351.9 32.9176 Q2353.68 32.5936 2355.46 32.5936 Q2365.59 32.5936 2371.5 38.1433 Q2377.42 43.6931 2377.42 53.1722 Q2377.42 62.9348 2371.34 68.3631 Q2365.26 73.7508 2354.2 73.7508 Q2350.4 73.7508 2346.43 73.1026 Q2342.5 72.4545 2338.28 71.1582 L2338.28 62.9348 Q2341.93 64.9198 2345.82 65.892 Q2349.71 66.8642 2354.04 66.8642 Q2361.05 66.8642 2365.14 63.1779 Q2369.23 59.4916 2369.23 53.1722 Q2369.23 46.8528 2365.14 43.1664 Q2361.05 39.4801 2354.04 39.4801 Q2350.76 39.4801 2347.48 40.2093 Q2344.24 40.9384 2340.84 42.4778 L2340.84 12.096 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M2426.96 86.3491 L2426.96 92.1419 L2383.86 92.1419 L2383.86 86.3491 L2426.96 86.3491 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M2452.52 17.4837 Q2446.2 17.4837 2443 23.7221 Q2439.84 29.92 2439.84 42.3968 Q2439.84 54.833 2443 61.0714 Q2446.2 67.2693 2452.52 67.2693 Q2458.88 67.2693 2462.04 61.0714 Q2465.24 54.833 2465.24 42.3968 Q2465.24 29.92 2462.04 23.7221 Q2458.88 17.4837 2452.52 17.4837 M2452.52 11.0023 Q2462.69 11.0023 2468.03 19.0636 Q2473.42 27.0843 2473.42 42.3968 Q2473.42 57.6687 2468.03 65.73 Q2462.69 73.7508 2452.52 73.7508 Q2442.35 73.7508 2436.96 65.73 Q2431.62 57.6687 2431.62 42.3968 Q2431.62 27.0843 2436.96 19.0636 Q2442.35 11.0023 2452.52 11.0023 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M2489.22 65.6895 L2502.59 65.6895 L2502.59 19.5497 L2488.05 22.4663 L2488.05 15.0127 L2502.51 12.096 L2510.69 12.096 L2510.69 65.6895 L2524.06 65.6895 L2524.06 72.576 L2489.22 72.576 L2489.22 65.6895 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M2562.22 87.2403 L2562.22 14.0809 L2603.7 14.0809 L2603.7 87.2403 L2562.22 87.2403 M2566.88 82.6222 L2599.08 82.6222 L2599.08 18.7395 L2566.88 18.7395 L2566.88 82.6222 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M2612 87.2403 L2612 14.0809 L2653.49 14.0809 L2653.49 87.2403 L2612 87.2403 M2616.66 82.6222 L2648.87 82.6222 L2648.87 18.7395 L2616.66 18.7395 L2616.66 82.6222 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M2688.16 87.2403 L2688.16 14.0809 L2729.64 14.0809 L2729.64 87.2403 L2688.16 87.2403 M2692.82 82.6222 L2725.02 82.6222 L2725.02 18.7395 L2692.82 18.7395 L2692.82 82.6222 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M2737.95 87.2403 L2737.95 14.0809 L2779.43 14.0809 L2779.43 87.2403 L2737.95 87.2403 M2742.61 82.6222 L2774.81 82.6222 L2774.81 18.7395 L2742.61 18.7395 L2742.61 82.6222 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M2787.73 87.2403 L2787.73 14.0809 L2829.21 14.0809 L2829.21 87.2403 L2787.73 87.2403 M2792.39 82.6222 L2824.6 82.6222 L2824.6 18.7395 L2792.39 18.7395 L2792.39 82.6222 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="404.274,517.687 404.274,413.136 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="404.274,500.815 404.274,486.109 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="447.042,592.944 447.042,495.206 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="447.042,589.052 447.042,500.815 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="489.81,678.459 489.81,587.243 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="489.81,603.758 489.81,589.052 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="532.578,694.135 532.578,603.515 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="532.578,691.994 532.578,603.758 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="575.346,724.362 575.346,631.532 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="575.346,706.7 575.346,691.994 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="618.115,794.936 618.115,703.852 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="618.115,794.936 618.115,706.7 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="660.883,868.467 660.883,779.857 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="660.883,809.642 660.883,794.936 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="703.651,868.467 703.651,780.101 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="703.651,809.642 703.651,794.936 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="746.419,809.642 746.419,645.682 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="746.419,809.642 746.419,691.994 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="789.187,794.936 789.187,705.989 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="789.187,794.936 789.187,706.7 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="831.955,839.055 831.955,585.181 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="831.955,809.642 831.955,589.052 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="874.723,663.504 874.723,572.571 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="874.723,603.758 874.723,589.052 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="917.492,546.001 917.492,456.459 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="917.492,500.815 917.492,486.109 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="960.26,535.119 960.26,436.176 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="960.26,500.815 960.26,486.109 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="1003.03,519.731 1003.03,350.239 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="1003.03,500.815 1003.03,383.167 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="1045.8,358.797 1045.8,254.957 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="1045.8,294.93 1045.8,280.224 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="1088.56,385.202 1088.56,286.809 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="1088.56,383.167 1088.56,294.93 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="1131.33,488.607 1131.33,396.193 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="1131.33,486.109 1131.33,397.873 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="1174.1,501.428 1174.1,264.892 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="1174.1,500.815 1174.1,280.224 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="1216.87,293.276 1216.87,178.153 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="1216.87,280.224 1216.87,191.988 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="1259.64,384.037 1259.64,277.428 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="1259.64,383.167 1259.64,294.93 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="1302.4,482.82 1302.4,369.899 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="1302.4,397.873 1302.4,383.167 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="1345.17,487.361 1345.17,390.649 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="1345.17,486.109 1345.17,397.873 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="1387.94,503.142 1387.94,411.96 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="1387.94,500.815 1387.94,486.109 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="1430.71,518.275 1430.71,364.965 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="1430.71,500.815 1430.71,383.167 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="1473.48,495.52 1473.48,396.384 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="1473.48,486.109 1473.48,397.873 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="1516.25,561.202 1516.25,470.79 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="1516.25,500.815 1516.25,486.109 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="1559.01,516.494 1559.01,425.371 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="1559.01,500.815 1559.01,486.109 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="1601.78,523.569 1601.78,415.781 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="1601.78,500.815 1601.78,486.109 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="1644.55,595.585 1644.55,491.274 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="1644.55,589.052 1644.55,500.815 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="1687.32,663.109 1687.32,480.37 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="1687.32,603.758 1687.32,486.109 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="1730.09,526.097 1730.09,417.353 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="1730.09,500.815 1730.09,486.109 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="1772.85,590.755 1772.85,494.149 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="1772.85,589.052 1772.85,500.815 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="1815.62,692.629 1815.62,603.116 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="1815.62,691.994 1815.62,603.758 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="1858.39,780.519 1858.39,690.557 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="1858.39,706.7 1858.39,691.994 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="1901.16,794.936 1901.16,704.727 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="1901.16,794.936 1901.16,706.7 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="1943.93,897.879 1943.93,809.642 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="1943.93,897.879 1943.93,809.642 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="1986.7,1000.82 1986.7,912.585 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="1986.7,1000.82 1986.7,912.585 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="2029.46,1103.76 2029.46,1015.53 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="2029.46,1103.76 2029.46,1015.53 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="2072.23,1015.53 2072.23,927.291 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="2072.23,1015.53 2072.23,1000.82 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="2115,1030.23 2115,941.997 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="2115,1015.53 2115,1000.82 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="2157.77,1103.76 2157.77,1015.53 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="2157.77,1103.76 2157.77,1015.53 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="2200.54,1118.47 2200.54,971.409 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="2200.54,1118.47 2200.54,1000.82 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="2243.3,1030.23 2243.3,941.997 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="2243.3,1015.53 2243.3,1000.82 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="2286.07,1030.23 2286.07,941.997 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="2286.07,1015.53 2286.07,1000.82 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="2328.84,1103.76 2328.84,1015.53 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="2328.84,1103.76 2328.84,1015.53 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="2371.61,1118.47 2371.61,1030.23 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="2371.61,1118.47 2371.61,1103.76 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="2414.38,1118.47 2414.38,1030.23 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="2414.38,1118.47 2414.38,1103.76 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="2457.14,1044.94 2457.14,956.703 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="2457.14,1015.53 2457.14,1000.82 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="2499.91,1103.76 2499.91,1015.53 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="2499.91,1103.76 2499.91,1015.53 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="2542.68,1206.71 2542.68,1118.47 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="2542.68,1206.71 2542.68,1118.47 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="2585.45,1309.65 2585.45,1221.41 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="2585.45,1309.65 2585.45,1221.41 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="2628.22,1324.35 2628.22,1236.12 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="2628.22,1324.35 2628.22,1309.65 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="2670.99,1412.59 2670.99,1324.35 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="2670.99,1412.59 2670.99,1324.35 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="2713.75,1442 2713.75,1206.71 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="2713.75,1427.3 2713.75,1206.71 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="2756.52,1309.65 2756.52,1221.41 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="2756.52,1309.65 2756.52,1221.41 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="2799.29,1412.59 2799.29,1324.35 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="2799.29,1412.59 2799.29,1324.35 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="2842.06,1456.71 2842.06,1280.24 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="2842.06,1427.3 2842.06,1309.65 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="2884.83,1353.77 2884.83,1265.53 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="2884.83,1324.35 2884.83,1309.65 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="2927.59,1353.77 2927.59,1265.53 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="2927.59,1324.35 2927.59,1309.65 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="2970.36,1250.82 2970.36,1162.59 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="2970.36,1221.41 2970.36,1206.71 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="3013.13,1250.82 3013.13,1162.59 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="3013.13,1221.41 3013.13,1206.71 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="3055.9,1206.71 3055.9,1118.47 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="3055.9,1206.71 3055.9,1118.47 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="3098.67,1265.53 3098.67,1089.06 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="3098.67,1221.41 3098.67,1103.76 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="3141.44,1118.47 3141.44,1030.23 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="3141.44,1118.47 3141.44,1103.76 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="3184.2,1162.59 3184.2,1074.35 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="3184.2,1118.47 3184.2,1103.76 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="3226.97,1206.71 3226.97,1118.47 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="3226.97,1206.71 3226.97,1118.47 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="3269.74,1265.53 3269.74,1177.29 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="3269.74,1221.41 3269.74,1206.71 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="3312.51,1250.82 3312.51,1162.59 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="3312.51,1221.41 3312.51,1206.71 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="3355.28,1309.65 3355.28,1221.41 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="3355.28,1309.65 3355.28,1221.41 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="3398.04,1412.59 3398.04,1324.35 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="3398.04,1412.59 3398.04,1324.35 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="3440.81,1515.53 3440.81,1427.3 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="3440.81,1515.53 3440.81,1427.3 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="3483.58,1589.06 3483.58,1500.83 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="3483.58,1530.24 3483.58,1515.53 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="3526.35,1618.48 3526.35,1530.24 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="3526.35,1618.48 3526.35,1530.24 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="3569.12,1647.89 3569.12,1559.65 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="3569.12,1633.18 3569.12,1618.48 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="3611.88,1677.3 3611.88,1589.06 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="3611.88,1633.18 3611.88,1618.48 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="3654.65,1721.42 3654.65,1633.18 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="3654.65,1721.42 3654.65,1633.18 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="3697.42,1824.36 3697.42,1736.12 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="3697.42,1824.36 3697.42,1736.12 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="3740.19,1809.65 3740.19,1721.42 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="3740.19,1736.12 3740.19,1721.42 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="3782.96,1824.36 3782.96,1736.12 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="3782.96,1824.36 3782.96,1736.12 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="3825.73,1927.3 3825.73,1839.07 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="3825.73,1927.3 3825.73,1839.07 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="3868.49,1839.07 3868.49,1603.77 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="3868.49,1839.07 3868.49,1618.48 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="3911.26,1618.48 3911.26,1530.24 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="3911.26,1618.48 3911.26,1530.24 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="3954.03,1721.42 3954.03,1633.18 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="3954.03,1721.42 3954.03,1633.18 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="3996.8,1736.12 3996.8,1559.65 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="3996.8,1736.12 3996.8,1618.48 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="4039.57,1677.3 4039.57,1589.06 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="4039.57,1633.18 4039.57,1618.48 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="4082.33,1721.42 4082.33,1633.18 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="4082.33,1721.42 4082.33,1633.18 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="4125.1,1824.36 4125.1,1736.12 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="4125.1,1824.36 4125.1,1736.12 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="4167.87,1839.07 4167.87,1618.48 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="4167.87,1839.07 4167.87,1618.48 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="4210.64,1721.42 4210.64,1633.18 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="4210.64,1721.42 4210.64,1633.18 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="4253.41,1824.36 4253.41,1736.12 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="4253.41,1824.36 4253.41,1736.12 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="4296.18,1839.07 4296.18,1750.83 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="4296.18,1839.07 4296.18,1824.36 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="4338.94,1927.3 4338.94,1839.07 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="4338.94,1927.3 4338.94,1839.07 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="4381.71,1927.3 4381.71,1839.07 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="4381.71,1927.3 4381.71,1839.07 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="4424.48,2000.83 4424.48,1912.6 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="4424.48,1942.01 4424.48,1927.3 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="4467.25,2000.83 4467.25,1912.6 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="4467.25,1942.01 4467.25,1927.3 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="4510.02,2000.83 4510.02,1824.36 "/>
<polyline clip-path="url(#clip002)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="4510.02,1942.01 4510.02,1824.36 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="4552.78,1927.3 4552.78,1839.07 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="4552.78,1927.3 4552.78,1839.07 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="4595.55,1927.3 4595.55,1839.07 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="4595.55,1927.3 4595.55,1839.07 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:4.8; stroke-opacity:0.9; fill:none" points="4626.1,2000.83 4626.1,1942.01 "/>
<polyline clip-path="url(#clip002)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:28; stroke-opacity:0.85; fill:none" points="4626.1,2000.83 4626.1,1942.01 "/>
<path clip-path="url(#clip000)" d="M4090.88 382.274 L4603.58 382.274 L4603.58 187.874 L4090.88 187.874  Z" fill="#ffffff" fill-rule="evenodd" fill-opacity="1"/>
<polyline clip-path="url(#clip000)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:1; fill:none" points="4090.88,382.274 4603.58,382.274 4603.58,187.874 4090.88,187.874 4090.88,382.274 "/>
<polyline clip-path="url(#clip000)" style="stroke:#cc1919; stroke-linecap:round; stroke-linejoin:round; stroke-width:25; stroke-opacity:0.85; fill:none" points="4140.6,252.674 4438.95,252.674 "/>
<path clip-path="url(#clip000)" d="M4488.67 284.748 L4488.67 232.491 L4518.3 232.491 L4518.3 284.748 L4488.67 284.748 M4492 281.449 L4515 281.449 L4515 235.819 L4492 235.819 L4492 281.449 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M4524.23 284.748 L4524.23 232.491 L4553.86 232.491 L4553.86 284.748 L4524.23 284.748 M4527.56 281.449 L4550.56 281.449 L4550.56 235.819 L4527.56 235.819 L4527.56 281.449 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><polyline clip-path="url(#clip000)" style="stroke:#199919; stroke-linecap:round; stroke-linejoin:round; stroke-width:25; stroke-opacity:0.85; fill:none" points="4140.6,317.474 4438.95,317.474 "/>
<path clip-path="url(#clip000)" d="M4488.67 349.548 L4488.67 297.291 L4518.3 297.291 L4518.3 349.548 L4488.67 349.548 M4492 346.249 L4515 346.249 L4515 300.619 L4492 300.619 L4492 346.249 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M4524.23 349.548 L4524.23 297.291 L4553.86 297.291 L4553.86 349.548 L4524.23 349.548 M4527.56 346.249 L4550.56 346.249 L4550.56 300.619 L4527.56 300.619 L4527.56 346.249 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M277.619 3011.5 L4752.76 3011.5 L4752.76 2367.49 L277.619 2367.49  Z" fill="#ffffff" fill-rule="evenodd" fill-opacity="1"/>
<defs>
  <clipPath id="clip003">
    <rect x="277" y="2367" width="4476" height="645"/>
  </clipPath>
</defs>
<polyline clip-path="url(#clip003)" style="stroke:#d3d3d3; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.3; fill:none" points="1182.79,3011.5 1182.79,2367.49 "/>
<polyline clip-path="url(#clip003)" style="stroke:#d3d3d3; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.3; fill:none" points="3280.24,3011.5 3280.24,2367.49 "/>
<polyline clip-path="url(#clip003)" style="stroke:#d3d3d3; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.3; fill:none" points="277.619,3011.5 4752.76,3011.5 "/>
<polyline clip-path="url(#clip003)" style="stroke:#d3d3d3; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.3; fill:none" points="277.619,2850.5 4752.76,2850.5 "/>
<polyline clip-path="url(#clip003)" style="stroke:#d3d3d3; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.3; fill:none" points="277.619,2689.49 4752.76,2689.49 "/>
<polyline clip-path="url(#clip003)" style="stroke:#d3d3d3; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.3; fill:none" points="277.619,2528.49 4752.76,2528.49 "/>
<polyline clip-path="url(#clip003)" style="stroke:#d3d3d3; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.3; fill:none" points="277.619,2367.49 4752.76,2367.49 "/>
<polyline clip-path="url(#clip000)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:1; fill:none" points="277.619,3011.5 4752.76,3011.5 "/>
<polyline clip-path="url(#clip000)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:1; fill:none" points="1182.79,3011.5 1182.79,2992.6 "/>
<polyline clip-path="url(#clip000)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:1; fill:none" points="3280.24,3011.5 3280.24,2992.6 "/>
<path clip-path="url(#clip000)" d="M1054.59 3089.16 L1070.91 3089.16 L1070.91 3093.1 L1048.96 3093.1 L1048.96 3089.16 Q1051.62 3086.41 1056.21 3081.78 Q1060.81 3077.13 1062 3075.79 Q1064.24 3073.26 1065.12 3071.53 Q1066.02 3069.77 1066.02 3068.08 Q1066.02 3065.32 1064.08 3063.59 Q1062.16 3061.85 1059.06 3061.85 Q1056.86 3061.85 1054.4 3062.61 Q1051.97 3063.38 1049.19 3064.93 L1049.19 3060.21 Q1052.02 3059.07 1054.47 3058.49 Q1056.93 3057.91 1058.96 3057.91 Q1064.33 3057.91 1067.53 3060.6 Q1070.72 3063.29 1070.72 3067.78 Q1070.72 3069.91 1069.91 3071.83 Q1069.12 3073.72 1067.02 3076.32 Q1066.44 3076.99 1063.34 3080.21 Q1060.24 3083.4 1054.59 3089.16 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M1090.72 3061.62 Q1087.11 3061.62 1085.28 3065.18 Q1083.48 3068.72 1083.48 3075.85 Q1083.48 3082.96 1085.28 3086.53 Q1087.11 3090.07 1090.72 3090.07 Q1094.36 3090.07 1096.16 3086.53 Q1097.99 3082.96 1097.99 3075.85 Q1097.99 3068.72 1096.16 3065.18 Q1094.36 3061.62 1090.72 3061.62 M1090.72 3057.91 Q1096.53 3057.91 1099.59 3062.52 Q1102.67 3067.1 1102.67 3075.85 Q1102.67 3084.58 1099.59 3089.19 Q1096.53 3093.77 1090.72 3093.77 Q1084.91 3093.77 1081.83 3089.19 Q1078.78 3084.58 1078.78 3075.85 Q1078.78 3067.1 1081.83 3062.52 Q1084.91 3057.91 1090.72 3057.91 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M1114.91 3089.16 L1131.23 3089.16 L1131.23 3093.1 L1109.29 3093.1 L1109.29 3089.16 Q1111.95 3086.41 1116.53 3081.78 Q1121.14 3077.13 1122.32 3075.79 Q1124.56 3073.26 1125.44 3071.53 Q1126.35 3069.77 1126.35 3068.08 Q1126.35 3065.32 1124.4 3063.59 Q1122.48 3061.85 1119.38 3061.85 Q1117.18 3061.85 1114.73 3062.61 Q1112.3 3063.38 1109.52 3064.93 L1109.52 3060.21 Q1112.34 3059.07 1114.8 3058.49 Q1117.25 3057.91 1119.29 3057.91 Q1124.66 3057.91 1127.85 3060.6 Q1131.05 3063.29 1131.05 3067.78 Q1131.05 3069.91 1130.24 3071.83 Q1129.45 3073.72 1127.34 3076.32 Q1126.76 3076.99 1123.66 3080.21 Q1120.56 3083.4 1114.91 3089.16 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M1153.89 3062.61 L1142.09 3081.06 L1153.89 3081.06 L1153.89 3062.61 M1152.67 3058.54 L1158.55 3058.54 L1158.55 3081.06 L1163.48 3081.06 L1163.48 3084.95 L1158.55 3084.95 L1158.55 3093.1 L1153.89 3093.1 L1153.89 3084.95 L1138.29 3084.95 L1138.29 3080.44 L1152.67 3058.54 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M1168.45 3078.22 L1180.93 3078.22 L1180.93 3082.01 L1168.45 3082.01 L1168.45 3078.22 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M1198.31 3061.62 Q1194.7 3061.62 1192.87 3065.18 Q1191.07 3068.72 1191.07 3075.85 Q1191.07 3082.96 1192.87 3086.53 Q1194.7 3090.07 1198.31 3090.07 Q1201.95 3090.07 1203.75 3086.53 Q1205.58 3082.96 1205.58 3075.85 Q1205.58 3068.72 1203.75 3065.18 Q1201.95 3061.62 1198.31 3061.62 M1198.31 3057.91 Q1204.12 3057.91 1207.18 3062.52 Q1210.26 3067.1 1210.26 3075.85 Q1210.26 3084.58 1207.18 3089.19 Q1204.12 3093.77 1198.31 3093.77 Q1192.5 3093.77 1189.43 3089.19 Q1186.37 3084.58 1186.37 3075.85 Q1186.37 3067.1 1189.43 3062.52 Q1192.5 3057.91 1198.31 3057.91 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M1219.29 3089.16 L1226.92 3089.16 L1226.92 3062.8 L1218.61 3064.47 L1218.61 3060.21 L1226.88 3058.54 L1231.55 3058.54 L1231.55 3089.16 L1239.19 3089.16 L1239.19 3093.1 L1219.29 3093.1 L1219.29 3089.16 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M1245.88 3078.22 L1258.36 3078.22 L1258.36 3082.01 L1245.88 3082.01 L1245.88 3078.22 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M1275.74 3061.62 Q1272.13 3061.62 1270.3 3065.18 Q1268.5 3068.72 1268.5 3075.85 Q1268.5 3082.96 1270.3 3086.53 Q1272.13 3090.07 1275.74 3090.07 Q1279.38 3090.07 1281.18 3086.53 Q1283.01 3082.96 1283.01 3075.85 Q1283.01 3068.72 1281.18 3065.18 Q1279.38 3061.62 1275.74 3061.62 M1275.74 3057.91 Q1281.55 3057.91 1284.61 3062.52 Q1287.69 3067.1 1287.69 3075.85 Q1287.69 3084.58 1284.61 3089.19 Q1281.55 3093.77 1275.74 3093.77 Q1269.93 3093.77 1266.86 3089.19 Q1263.8 3084.58 1263.8 3075.85 Q1263.8 3067.1 1266.86 3062.52 Q1269.93 3057.91 1275.74 3057.91 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M1296.72 3089.16 L1304.36 3089.16 L1304.36 3062.8 L1296.04 3064.47 L1296.04 3060.21 L1304.31 3058.54 L1308.98 3058.54 L1308.98 3089.16 L1316.62 3089.16 L1316.62 3093.1 L1296.72 3093.1 L1296.72 3089.16 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M3152.03 3089.16 L3168.35 3089.16 L3168.35 3093.1 L3146.41 3093.1 L3146.41 3089.16 Q3149.07 3086.41 3153.65 3081.78 Q3158.26 3077.13 3159.44 3075.79 Q3161.69 3073.26 3162.57 3071.53 Q3163.47 3069.77 3163.47 3068.08 Q3163.47 3065.32 3161.52 3063.59 Q3159.6 3061.85 3156.5 3061.85 Q3154.3 3061.85 3151.85 3062.61 Q3149.42 3063.38 3146.64 3064.93 L3146.64 3060.21 Q3149.46 3059.07 3151.92 3058.49 Q3154.37 3057.91 3156.41 3057.91 Q3161.78 3057.91 3164.97 3060.6 Q3168.17 3063.29 3168.17 3067.78 Q3168.17 3069.91 3167.36 3071.83 Q3166.57 3073.72 3164.46 3076.32 Q3163.89 3076.99 3160.78 3080.21 Q3157.68 3083.4 3152.03 3089.16 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M3188.17 3061.62 Q3184.56 3061.62 3182.73 3065.18 Q3180.92 3068.72 3180.92 3075.85 Q3180.92 3082.96 3182.73 3086.53 Q3184.56 3090.07 3188.17 3090.07 Q3191.8 3090.07 3193.61 3086.53 Q3195.44 3082.96 3195.44 3075.85 Q3195.44 3068.72 3193.61 3065.18 Q3191.8 3061.62 3188.17 3061.62 M3188.17 3057.91 Q3193.98 3057.91 3197.03 3062.52 Q3200.11 3067.1 3200.11 3075.85 Q3200.11 3084.58 3197.03 3089.19 Q3193.98 3093.77 3188.17 3093.77 Q3182.36 3093.77 3179.28 3089.19 Q3176.22 3084.58 3176.22 3075.85 Q3176.22 3067.1 3179.28 3062.52 Q3182.36 3057.91 3188.17 3057.91 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M3212.36 3089.16 L3228.68 3089.16 L3228.68 3093.1 L3206.73 3093.1 L3206.73 3089.16 Q3209.4 3086.41 3213.98 3081.78 Q3218.58 3077.13 3219.77 3075.79 Q3222.01 3073.26 3222.89 3071.53 Q3223.79 3069.77 3223.79 3068.08 Q3223.79 3065.32 3221.85 3063.59 Q3219.93 3061.85 3216.83 3061.85 Q3214.63 3061.85 3212.17 3062.61 Q3209.74 3063.38 3206.96 3064.93 L3206.96 3060.21 Q3209.79 3059.07 3212.24 3058.49 Q3214.7 3057.91 3216.73 3057.91 Q3222.1 3057.91 3225.3 3060.6 Q3228.49 3063.29 3228.49 3067.78 Q3228.49 3069.91 3227.68 3071.83 Q3226.9 3073.72 3224.79 3076.32 Q3224.21 3076.99 3221.11 3080.21 Q3218.01 3083.4 3212.36 3089.16 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M3238.54 3058.54 L3256.89 3058.54 L3256.89 3062.48 L3242.82 3062.48 L3242.82 3070.95 Q3243.84 3070.6 3244.86 3070.44 Q3245.88 3070.25 3246.89 3070.25 Q3252.68 3070.25 3256.06 3073.42 Q3259.44 3076.6 3259.44 3082.01 Q3259.44 3087.59 3255.97 3090.69 Q3252.5 3093.77 3246.18 3093.77 Q3244 3093.77 3241.73 3093.4 Q3239.49 3093.03 3237.08 3092.29 L3237.08 3087.59 Q3239.16 3088.72 3241.39 3089.28 Q3243.61 3089.84 3246.08 3089.84 Q3250.09 3089.84 3252.43 3087.73 Q3254.77 3085.62 3254.77 3082.01 Q3254.77 3078.4 3252.43 3076.29 Q3250.09 3074.19 3246.08 3074.19 Q3244.21 3074.19 3242.33 3074.6 Q3240.48 3075.02 3238.54 3075.9 L3238.54 3058.54 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M3265.9 3078.22 L3278.38 3078.22 L3278.38 3082.01 L3265.9 3082.01 L3265.9 3078.22 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M3295.76 3061.62 Q3292.15 3061.62 3290.32 3065.18 Q3288.52 3068.72 3288.52 3075.85 Q3288.52 3082.96 3290.32 3086.53 Q3292.15 3090.07 3295.76 3090.07 Q3299.39 3090.07 3301.2 3086.53 Q3303.03 3082.96 3303.03 3075.85 Q3303.03 3068.72 3301.2 3065.18 Q3299.39 3061.62 3295.76 3061.62 M3295.76 3057.91 Q3301.57 3057.91 3304.63 3062.52 Q3307.7 3067.1 3307.7 3075.85 Q3307.7 3084.58 3304.63 3089.19 Q3301.57 3093.77 3295.76 3093.77 Q3289.95 3093.77 3286.87 3089.19 Q3283.82 3084.58 3283.82 3075.85 Q3283.82 3067.1 3286.87 3062.52 Q3289.95 3057.91 3295.76 3057.91 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M3316.73 3089.16 L3324.37 3089.16 L3324.37 3062.8 L3316.06 3064.47 L3316.06 3060.21 L3324.33 3058.54 L3329 3058.54 L3329 3089.16 L3336.64 3089.16 L3336.64 3093.1 L3316.73 3093.1 L3316.73 3089.16 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M3343.33 3078.22 L3355.81 3078.22 L3355.81 3082.01 L3343.33 3082.01 L3343.33 3078.22 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M3373.19 3061.62 Q3369.58 3061.62 3367.75 3065.18 Q3365.95 3068.72 3365.95 3075.85 Q3365.95 3082.96 3367.75 3086.53 Q3369.58 3090.07 3373.19 3090.07 Q3376.82 3090.07 3378.63 3086.53 Q3380.46 3082.96 3380.46 3075.85 Q3380.46 3068.72 3378.63 3065.18 Q3376.82 3061.62 3373.19 3061.62 M3373.19 3057.91 Q3379 3057.91 3382.06 3062.52 Q3385.13 3067.1 3385.13 3075.85 Q3385.13 3084.58 3382.06 3089.19 Q3379 3093.77 3373.19 3093.77 Q3367.38 3093.77 3364.3 3089.19 Q3361.25 3084.58 3361.25 3075.85 Q3361.25 3067.1 3364.3 3062.52 Q3367.38 3057.91 3373.19 3057.91 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M3394.16 3089.16 L3401.8 3089.16 L3401.8 3062.8 L3393.49 3064.47 L3393.49 3060.21 L3401.76 3058.54 L3406.43 3058.54 L3406.43 3089.16 L3414.07 3089.16 L3414.07 3093.1 L3394.16 3093.1 L3394.16 3089.16 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M2482.59 3197.65 L2482.59 3145.4 L2512.22 3145.4 L2512.22 3197.65 L2482.59 3197.65 M2485.92 3194.36 L2508.92 3194.36 L2508.92 3148.73 L2485.92 3148.73 L2485.92 3194.36 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M2518.15 3197.65 L2518.15 3145.4 L2547.78 3145.4 L2547.78 3197.65 L2518.15 3197.65 M2521.48 3194.36 L2544.48 3194.36 L2544.48 3148.73 L2521.48 3148.73 L2521.48 3194.36 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><polyline clip-path="url(#clip000)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:1; fill:none" points="277.619,3011.5 277.619,2367.49 "/>
<polyline clip-path="url(#clip000)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:1; fill:none" points="277.619,3011.5 296.517,3011.5 "/>
<polyline clip-path="url(#clip000)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:1; fill:none" points="277.619,2850.5 296.517,2850.5 "/>
<polyline clip-path="url(#clip000)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:1; fill:none" points="277.619,2689.49 296.517,2689.49 "/>
<polyline clip-path="url(#clip000)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:1; fill:none" points="277.619,2528.49 296.517,2528.49 "/>
<polyline clip-path="url(#clip000)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:1; fill:none" points="277.619,2367.49 296.517,2367.49 "/>
<path clip-path="url(#clip000)" d="M193.675 2997.3 Q190.064 2997.3 188.235 3000.86 Q186.429 3004.4 186.429 3011.53 Q186.429 3018.64 188.235 3022.21 Q190.064 3025.75 193.675 3025.75 Q197.309 3025.75 199.114 3022.21 Q200.943 3018.64 200.943 3011.53 Q200.943 3004.4 199.114 3000.86 Q197.309 2997.3 193.675 2997.3 M193.675 2993.59 Q199.485 2993.59 202.54 2998.2 Q205.619 3002.78 205.619 3011.53 Q205.619 3020.26 202.54 3024.87 Q199.485 3029.45 193.675 3029.45 Q187.864 3029.45 184.786 3024.87 Q181.73 3020.26 181.73 3011.53 Q181.73 3002.78 184.786 2998.2 Q187.864 2993.59 193.675 2993.59 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M185.712 2863.84 L193.351 2863.84 L193.351 2837.48 L185.04 2839.14 L185.04 2834.88 L193.304 2833.22 L197.98 2833.22 L197.98 2863.84 L205.619 2863.84 L205.619 2867.78 L185.712 2867.78 L185.712 2863.84 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M189.3 2702.84 L205.619 2702.84 L205.619 2706.77 L183.675 2706.77 L183.675 2702.84 Q186.337 2700.08 190.92 2695.45 Q195.526 2690.8 196.707 2689.46 Q198.952 2686.94 199.832 2685.2 Q200.735 2683.44 200.735 2681.75 Q200.735 2679 198.79 2677.26 Q196.869 2675.52 193.767 2675.52 Q191.568 2675.52 189.114 2676.29 Q186.684 2677.05 183.906 2678.6 L183.906 2673.88 Q186.73 2672.75 189.184 2672.17 Q191.638 2671.59 193.675 2671.59 Q199.045 2671.59 202.239 2674.27 Q205.434 2676.96 205.434 2681.45 Q205.434 2683.58 204.624 2685.5 Q203.837 2687.4 201.73 2689.99 Q201.151 2690.66 198.05 2693.88 Q194.948 2697.07 189.3 2702.84 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M198.489 2527.14 Q201.846 2527.85 203.721 2530.12 Q205.619 2532.39 205.619 2535.72 Q205.619 2540.84 202.1 2543.64 Q198.582 2546.44 192.101 2546.44 Q189.925 2546.44 187.61 2546 Q185.318 2545.58 182.864 2544.73 L182.864 2540.21 Q184.809 2541.35 187.124 2541.93 Q189.439 2542.51 191.962 2542.51 Q196.36 2542.51 198.651 2540.77 Q200.966 2539.03 200.966 2535.72 Q200.966 2532.67 198.813 2530.95 Q196.684 2529.22 192.864 2529.22 L188.837 2529.22 L188.837 2525.38 L193.05 2525.38 Q196.499 2525.38 198.327 2524.01 Q200.156 2522.62 200.156 2520.03 Q200.156 2517.37 198.258 2515.95 Q196.383 2514.52 192.864 2514.52 Q190.943 2514.52 188.744 2514.94 Q186.545 2515.35 183.906 2516.23 L183.906 2512.07 Q186.568 2511.33 188.883 2510.95 Q191.221 2510.58 193.281 2510.58 Q198.605 2510.58 201.707 2513.01 Q204.809 2515.42 204.809 2519.54 Q204.809 2522.41 203.165 2524.4 Q201.522 2526.37 198.489 2527.14 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M196.036 2354.28 L184.23 2372.73 L196.036 2372.73 L196.036 2354.28 M194.809 2350.21 L200.688 2350.21 L200.688 2372.73 L205.619 2372.73 L205.619 2376.62 L200.688 2376.62 L200.688 2384.77 L196.036 2384.77 L196.036 2376.62 L180.434 2376.62 L180.434 2372.1 L194.809 2350.21 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M105.948 2739.87 L53.6917 2739.87 L53.6917 2710.24 L105.948 2710.24 L105.948 2739.87 M102.65 2736.54 L102.65 2713.54 L57.0193 2713.54 L57.0193 2736.54 L102.65 2736.54 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M105.948 2704.31 L53.6917 2704.31 L53.6917 2674.68 L105.948 2674.68 L105.948 2704.31 M102.65 2700.98 L102.65 2677.98 L57.0193 2677.98 L57.0193 2700.98 L102.65 2700.98 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M105.948 2668.75 L53.6917 2668.75 L53.6917 2639.12 L105.948 2639.12 L105.948 2668.75 M102.65 2665.42 L102.65 2642.42 L57.0193 2642.42 L57.0193 2665.42 L102.65 2665.42 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M2454.74 2327.43 L2454.74 2264.72 L2490.29 2264.72 L2490.29 2327.43 L2454.74 2327.43 M2458.73 2323.47 L2486.33 2323.47 L2486.33 2268.72 L2458.73 2268.72 L2458.73 2323.47 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M2497.41 2327.43 L2497.41 2264.72 L2532.97 2264.72 L2532.97 2327.43 L2497.41 2327.43 M2501.4 2323.47 L2529.01 2323.47 L2529.01 2268.72 L2501.4 2268.72 L2501.4 2323.47 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M2540.08 2327.43 L2540.08 2264.72 L2575.64 2264.72 L2575.64 2327.43 L2540.08 2327.43 M2544.08 2323.47 L2571.68 2323.47 L2571.68 2268.72 L2544.08 2268.72 L2544.08 2323.47 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip003)" d="M523.759 2850.5 L523.759 3011.5 L546.682 3011.5 L546.682 2850.5 L523.759 2850.5 L523.759 2850.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="523.759,2850.5 523.759,3011.5 546.682,3011.5 546.682,2850.5 523.759,2850.5 "/>
<path clip-path="url(#clip003)" d="M563.875 3011.5 L563.875 3011.5 L586.797 3011.5 L586.797 3011.5 L563.875 3011.5 L563.875 3011.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="563.875,3011.5 563.875,3011.5 586.797,3011.5 563.875,3011.5 "/>
<path clip-path="url(#clip003)" d="M603.99 2850.5 L603.99 3011.5 L626.913 3011.5 L626.913 2850.5 L603.99 2850.5 L603.99 2850.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="603.99,2850.5 603.99,3011.5 626.913,3011.5 626.913,2850.5 603.99,2850.5 "/>
<path clip-path="url(#clip003)" d="M644.105 3011.5 L644.105 3011.5 L667.028 3011.5 L667.028 3011.5 L644.105 3011.5 L644.105 3011.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="644.105,3011.5 644.105,3011.5 667.028,3011.5 644.105,3011.5 "/>
<path clip-path="url(#clip003)" d="M684.22 2850.5 L684.22 3011.5 L707.143 3011.5 L707.143 2850.5 L684.22 2850.5 L684.22 2850.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="684.22,2850.5 684.22,3011.5 707.143,3011.5 707.143,2850.5 684.22,2850.5 "/>
<path clip-path="url(#clip003)" d="M724.335 3011.5 L724.335 3011.5 L747.258 3011.5 L747.258 3011.5 L724.335 3011.5 L724.335 3011.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="724.335,3011.5 724.335,3011.5 747.258,3011.5 724.335,3011.5 "/>
<path clip-path="url(#clip003)" d="M764.45 2850.5 L764.45 3011.5 L787.373 3011.5 L787.373 2850.5 L764.45 2850.5 L764.45 2850.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="764.45,2850.5 764.45,3011.5 787.373,3011.5 787.373,2850.5 764.45,2850.5 "/>
<path clip-path="url(#clip003)" d="M804.565 2850.5 L804.565 3011.5 L827.488 3011.5 L827.488 2850.5 L804.565 2850.5 L804.565 2850.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="804.565,2850.5 804.565,3011.5 827.488,3011.5 827.488,2850.5 804.565,2850.5 "/>
<path clip-path="url(#clip003)" d="M844.68 2689.49 L844.68 3011.5 L867.603 3011.5 L867.603 2689.49 L844.68 2689.49 L844.68 2689.49  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="844.68,2689.49 844.68,3011.5 867.603,3011.5 867.603,2689.49 844.68,2689.49 "/>
<path clip-path="url(#clip003)" d="M884.795 3011.5 L884.795 3011.5 L907.718 3011.5 L907.718 3011.5 L884.795 3011.5 L884.795 3011.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="884.795,3011.5 884.795,3011.5 907.718,3011.5 884.795,3011.5 "/>
<path clip-path="url(#clip003)" d="M924.91 2528.49 L924.91 3011.5 L947.833 3011.5 L947.833 2528.49 L924.91 2528.49 L924.91 2528.49  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="924.91,2528.49 924.91,3011.5 947.833,3011.5 947.833,2528.49 924.91,2528.49 "/>
<path clip-path="url(#clip003)" d="M965.026 2850.5 L965.026 3011.5 L987.948 3011.5 L987.948 2850.5 L965.026 2850.5 L965.026 2850.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="965.026,2850.5 965.026,3011.5 987.948,3011.5 987.948,2850.5 965.026,2850.5 "/>
<path clip-path="url(#clip003)" d="M1005.14 2689.49 L1005.14 3011.5 L1028.06 3011.5 L1028.06 2689.49 L1005.14 2689.49 L1005.14 2689.49  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="1005.14,2689.49 1005.14,3011.5 1028.06,3011.5 1028.06,2689.49 1005.14,2689.49 "/>
<path clip-path="url(#clip003)" d="M1045.26 2850.5 L1045.26 3011.5 L1068.18 3011.5 L1068.18 2850.5 L1045.26 2850.5 L1045.26 2850.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="1045.26,2850.5 1045.26,3011.5 1068.18,3011.5 1068.18,2850.5 1045.26,2850.5 "/>
<path clip-path="url(#clip003)" d="M1085.37 2689.49 L1085.37 3011.5 L1108.29 3011.5 L1108.29 2689.49 L1085.37 2689.49 L1085.37 2689.49  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="1085.37,2689.49 1085.37,3011.5 1108.29,3011.5 1108.29,2689.49 1085.37,2689.49 "/>
<path clip-path="url(#clip003)" d="M1125.49 2689.49 L1125.49 3011.5 L1148.41 3011.5 L1148.41 2689.49 L1125.49 2689.49 L1125.49 2689.49  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="1125.49,2689.49 1125.49,3011.5 1148.41,3011.5 1148.41,2689.49 1125.49,2689.49 "/>
<path clip-path="url(#clip003)" d="M1165.6 3011.5 L1165.6 3011.5 L1188.52 3011.5 L1188.52 3011.5 L1165.6 3011.5 L1165.6 3011.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="1165.6,3011.5 1165.6,3011.5 1188.52,3011.5 1165.6,3011.5 "/>
<path clip-path="url(#clip003)" d="M1205.72 3011.5 L1205.72 3011.5 L1228.64 3011.5 L1228.64 3011.5 L1205.72 3011.5 L1205.72 3011.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="1205.72,3011.5 1205.72,3011.5 1228.64,3011.5 1205.72,3011.5 "/>
<path clip-path="url(#clip003)" d="M1245.83 2528.49 L1245.83 3011.5 L1268.75 3011.5 L1268.75 2528.49 L1245.83 2528.49 L1245.83 2528.49  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="1245.83,2528.49 1245.83,3011.5 1268.75,3011.5 1268.75,2528.49 1245.83,2528.49 "/>
<path clip-path="url(#clip003)" d="M1285.95 2850.5 L1285.95 3011.5 L1308.87 3011.5 L1308.87 2850.5 L1285.95 2850.5 L1285.95 2850.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="1285.95,2850.5 1285.95,3011.5 1308.87,3011.5 1308.87,2850.5 1285.95,2850.5 "/>
<path clip-path="url(#clip003)" d="M1326.06 3011.5 L1326.06 3011.5 L1348.98 3011.5 L1348.98 3011.5 L1326.06 3011.5 L1326.06 3011.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="1326.06,3011.5 1326.06,3011.5 1348.98,3011.5 1326.06,3011.5 "/>
<path clip-path="url(#clip003)" d="M1366.18 2850.5 L1366.18 3011.5 L1389.1 3011.5 L1389.1 2850.5 L1366.18 2850.5 L1366.18 2850.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="1366.18,2850.5 1366.18,3011.5 1389.1,3011.5 1389.1,2850.5 1366.18,2850.5 "/>
<path clip-path="url(#clip003)" d="M1406.29 3011.5 L1406.29 3011.5 L1429.21 3011.5 L1429.21 3011.5 L1406.29 3011.5 L1406.29 3011.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="1406.29,3011.5 1406.29,3011.5 1429.21,3011.5 1406.29,3011.5 "/>
<path clip-path="url(#clip003)" d="M1446.41 2850.5 L1446.41 3011.5 L1469.33 3011.5 L1469.33 2850.5 L1446.41 2850.5 L1446.41 2850.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="1446.41,2850.5 1446.41,3011.5 1469.33,3011.5 1469.33,2850.5 1446.41,2850.5 "/>
<path clip-path="url(#clip003)" d="M1486.52 2689.49 L1486.52 3011.5 L1509.44 3011.5 L1509.44 2689.49 L1486.52 2689.49 L1486.52 2689.49  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="1486.52,2689.49 1486.52,3011.5 1509.44,3011.5 1509.44,2689.49 1486.52,2689.49 "/>
<path clip-path="url(#clip003)" d="M1526.64 3011.5 L1526.64 3011.5 L1549.56 3011.5 L1549.56 3011.5 L1526.64 3011.5 L1526.64 3011.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="1526.64,3011.5 1526.64,3011.5 1549.56,3011.5 1526.64,3011.5 "/>
<path clip-path="url(#clip003)" d="M1566.75 2850.5 L1566.75 3011.5 L1589.67 3011.5 L1589.67 2850.5 L1566.75 2850.5 L1566.75 2850.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="1566.75,2850.5 1566.75,3011.5 1589.67,3011.5 1589.67,2850.5 1566.75,2850.5 "/>
<path clip-path="url(#clip003)" d="M1606.87 2850.5 L1606.87 3011.5 L1629.79 3011.5 L1629.79 2850.5 L1606.87 2850.5 L1606.87 2850.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="1606.87,2850.5 1606.87,3011.5 1629.79,3011.5 1629.79,2850.5 1606.87,2850.5 "/>
<path clip-path="url(#clip003)" d="M1646.98 2850.5 L1646.98 3011.5 L1669.91 3011.5 L1669.91 2850.5 L1646.98 2850.5 L1646.98 2850.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="1646.98,2850.5 1646.98,3011.5 1669.91,3011.5 1669.91,2850.5 1646.98,2850.5 "/>
<path clip-path="url(#clip003)" d="M1687.1 3011.5 L1687.1 3011.5 L1710.02 3011.5 L1710.02 3011.5 L1687.1 3011.5 L1687.1 3011.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="1687.1,3011.5 1687.1,3011.5 1710.02,3011.5 1687.1,3011.5 "/>
<path clip-path="url(#clip003)" d="M1727.21 2689.49 L1727.21 3011.5 L1750.14 3011.5 L1750.14 2689.49 L1727.21 2689.49 L1727.21 2689.49  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="1727.21,2689.49 1727.21,3011.5 1750.14,3011.5 1750.14,2689.49 1727.21,2689.49 "/>
<path clip-path="url(#clip003)" d="M1767.33 2850.5 L1767.33 3011.5 L1790.25 3011.5 L1790.25 2850.5 L1767.33 2850.5 L1767.33 2850.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="1767.33,2850.5 1767.33,3011.5 1790.25,3011.5 1790.25,2850.5 1767.33,2850.5 "/>
<path clip-path="url(#clip003)" d="M1807.44 3011.5 L1807.44 3011.5 L1830.37 3011.5 L1830.37 3011.5 L1807.44 3011.5 L1807.44 3011.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="1807.44,3011.5 1807.44,3011.5 1830.37,3011.5 1807.44,3011.5 "/>
<path clip-path="url(#clip003)" d="M1847.56 3011.5 L1847.56 3011.5 L1870.48 3011.5 L1870.48 3011.5 L1847.56 3011.5 L1847.56 3011.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="1847.56,3011.5 1847.56,3011.5 1870.48,3011.5 1847.56,3011.5 "/>
<path clip-path="url(#clip003)" d="M1887.67 2850.5 L1887.67 3011.5 L1910.6 3011.5 L1910.6 2850.5 L1887.67 2850.5 L1887.67 2850.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="1887.67,2850.5 1887.67,3011.5 1910.6,3011.5 1910.6,2850.5 1887.67,2850.5 "/>
<path clip-path="url(#clip003)" d="M1927.79 3011.5 L1927.79 3011.5 L1950.71 3011.5 L1950.71 3011.5 L1927.79 3011.5 L1927.79 3011.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="1927.79,3011.5 1927.79,3011.5 1950.71,3011.5 1927.79,3011.5 "/>
<path clip-path="url(#clip003)" d="M1967.9 3011.5 L1967.9 3011.5 L1990.83 3011.5 L1990.83 3011.5 L1967.9 3011.5 L1967.9 3011.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="1967.9,3011.5 1967.9,3011.5 1990.83,3011.5 1967.9,3011.5 "/>
<path clip-path="url(#clip003)" d="M2008.02 3011.5 L2008.02 3011.5 L2030.94 3011.5 L2030.94 3011.5 L2008.02 3011.5 L2008.02 3011.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="2008.02,3011.5 2008.02,3011.5 2030.94,3011.5 2008.02,3011.5 "/>
<path clip-path="url(#clip003)" d="M2048.13 3011.5 L2048.13 3011.5 L2071.06 3011.5 L2071.06 3011.5 L2048.13 3011.5 L2048.13 3011.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="2048.13,3011.5 2048.13,3011.5 2071.06,3011.5 2048.13,3011.5 "/>
<path clip-path="url(#clip003)" d="M2088.25 2689.49 L2088.25 3011.5 L2111.17 3011.5 L2111.17 2689.49 L2088.25 2689.49 L2088.25 2689.49  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="2088.25,2689.49 2088.25,3011.5 2111.17,3011.5 2111.17,2689.49 2088.25,2689.49 "/>
<path clip-path="url(#clip003)" d="M2128.36 2850.5 L2128.36 3011.5 L2151.29 3011.5 L2151.29 2850.5 L2128.36 2850.5 L2128.36 2850.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="2128.36,2850.5 2128.36,3011.5 2151.29,3011.5 2151.29,2850.5 2128.36,2850.5 "/>
<path clip-path="url(#clip003)" d="M2168.48 3011.5 L2168.48 3011.5 L2191.4 3011.5 L2191.4 3011.5 L2168.48 3011.5 L2168.48 3011.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="2168.48,3011.5 2168.48,3011.5 2191.4,3011.5 2168.48,3011.5 "/>
<path clip-path="url(#clip003)" d="M2208.59 2689.49 L2208.59 3011.5 L2231.52 3011.5 L2231.52 2689.49 L2208.59 2689.49 L2208.59 2689.49  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="2208.59,2689.49 2208.59,3011.5 2231.52,3011.5 2231.52,2689.49 2208.59,2689.49 "/>
<path clip-path="url(#clip003)" d="M2248.71 2850.5 L2248.71 3011.5 L2271.63 3011.5 L2271.63 2850.5 L2248.71 2850.5 L2248.71 2850.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="2248.71,2850.5 2248.71,3011.5 2271.63,3011.5 2271.63,2850.5 2248.71,2850.5 "/>
<path clip-path="url(#clip003)" d="M2288.82 2850.5 L2288.82 3011.5 L2311.75 3011.5 L2311.75 2850.5 L2288.82 2850.5 L2288.82 2850.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="2288.82,2850.5 2288.82,3011.5 2311.75,3011.5 2311.75,2850.5 2288.82,2850.5 "/>
<path clip-path="url(#clip003)" d="M2328.94 3011.5 L2328.94 3011.5 L2351.86 3011.5 L2351.86 3011.5 L2328.94 3011.5 L2328.94 3011.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="2328.94,3011.5 2328.94,3011.5 2351.86,3011.5 2328.94,3011.5 "/>
<path clip-path="url(#clip003)" d="M2369.05 2850.5 L2369.05 3011.5 L2391.98 3011.5 L2391.98 2850.5 L2369.05 2850.5 L2369.05 2850.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="2369.05,2850.5 2369.05,3011.5 2391.98,3011.5 2391.98,2850.5 2369.05,2850.5 "/>
<path clip-path="url(#clip003)" d="M2409.17 2850.5 L2409.17 3011.5 L2432.09 3011.5 L2432.09 2850.5 L2409.17 2850.5 L2409.17 2850.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="2409.17,2850.5 2409.17,3011.5 2432.09,3011.5 2432.09,2850.5 2409.17,2850.5 "/>
<path clip-path="url(#clip003)" d="M2449.28 2689.49 L2449.28 3011.5 L2472.21 3011.5 L2472.21 2689.49 L2449.28 2689.49 L2449.28 2689.49  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="2449.28,2689.49 2449.28,3011.5 2472.21,3011.5 2472.21,2689.49 2449.28,2689.49 "/>
<path clip-path="url(#clip003)" d="M2489.4 3011.5 L2489.4 3011.5 L2512.32 3011.5 L2512.32 3011.5 L2489.4 3011.5 L2489.4 3011.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="2489.4,3011.5 2489.4,3011.5 2512.32,3011.5 2489.4,3011.5 "/>
<path clip-path="url(#clip003)" d="M2529.51 3011.5 L2529.51 3011.5 L2552.44 3011.5 L2552.44 3011.5 L2529.51 3011.5 L2529.51 3011.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="2529.51,3011.5 2529.51,3011.5 2552.44,3011.5 2529.51,3011.5 "/>
<path clip-path="url(#clip003)" d="M2569.63 3011.5 L2569.63 3011.5 L2592.55 3011.5 L2592.55 3011.5 L2569.63 3011.5 L2569.63 3011.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="2569.63,3011.5 2569.63,3011.5 2592.55,3011.5 2569.63,3011.5 "/>
<path clip-path="url(#clip003)" d="M2609.74 2850.5 L2609.74 3011.5 L2632.67 3011.5 L2632.67 2850.5 L2609.74 2850.5 L2609.74 2850.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="2609.74,2850.5 2609.74,3011.5 2632.67,3011.5 2632.67,2850.5 2609.74,2850.5 "/>
<path clip-path="url(#clip003)" d="M2649.86 3011.5 L2649.86 3011.5 L2672.78 3011.5 L2672.78 3011.5 L2649.86 3011.5 L2649.86 3011.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="2649.86,3011.5 2649.86,3011.5 2672.78,3011.5 2649.86,3011.5 "/>
<path clip-path="url(#clip003)" d="M2689.97 2528.49 L2689.97 3011.5 L2712.9 3011.5 L2712.9 2528.49 L2689.97 2528.49 L2689.97 2528.49  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="2689.97,2528.49 2689.97,3011.5 2712.9,3011.5 2712.9,2528.49 2689.97,2528.49 "/>
<path clip-path="url(#clip003)" d="M2730.09 3011.5 L2730.09 3011.5 L2753.01 3011.5 L2753.01 3011.5 L2730.09 3011.5 L2730.09 3011.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="2730.09,3011.5 2730.09,3011.5 2753.01,3011.5 2730.09,3011.5 "/>
<path clip-path="url(#clip003)" d="M2770.2 3011.5 L2770.2 3011.5 L2793.13 3011.5 L2793.13 3011.5 L2770.2 3011.5 L2770.2 3011.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="2770.2,3011.5 2770.2,3011.5 2793.13,3011.5 2770.2,3011.5 "/>
<path clip-path="url(#clip003)" d="M2810.32 2689.49 L2810.32 3011.5 L2833.24 3011.5 L2833.24 2689.49 L2810.32 2689.49 L2810.32 2689.49  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="2810.32,2689.49 2810.32,3011.5 2833.24,3011.5 2833.24,2689.49 2810.32,2689.49 "/>
<path clip-path="url(#clip003)" d="M2850.44 2850.5 L2850.44 3011.5 L2873.36 3011.5 L2873.36 2850.5 L2850.44 2850.5 L2850.44 2850.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="2850.44,2850.5 2850.44,3011.5 2873.36,3011.5 2873.36,2850.5 2850.44,2850.5 "/>
<path clip-path="url(#clip003)" d="M2890.55 2850.5 L2890.55 3011.5 L2913.47 3011.5 L2913.47 2850.5 L2890.55 2850.5 L2890.55 2850.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="2890.55,2850.5 2890.55,3011.5 2913.47,3011.5 2913.47,2850.5 2890.55,2850.5 "/>
<path clip-path="url(#clip003)" d="M2930.67 2689.49 L2930.67 3011.5 L2953.59 3011.5 L2953.59 2689.49 L2930.67 2689.49 L2930.67 2689.49  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="2930.67,2689.49 2930.67,3011.5 2953.59,3011.5 2953.59,2689.49 2930.67,2689.49 "/>
<path clip-path="url(#clip003)" d="M2970.78 2850.5 L2970.78 3011.5 L2993.7 3011.5 L2993.7 2850.5 L2970.78 2850.5 L2970.78 2850.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="2970.78,2850.5 2970.78,3011.5 2993.7,3011.5 2993.7,2850.5 2970.78,2850.5 "/>
<path clip-path="url(#clip003)" d="M3010.9 2850.5 L3010.9 3011.5 L3033.82 3011.5 L3033.82 2850.5 L3010.9 2850.5 L3010.9 2850.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="3010.9,2850.5 3010.9,3011.5 3033.82,3011.5 3033.82,2850.5 3010.9,2850.5 "/>
<path clip-path="url(#clip003)" d="M3051.01 2689.49 L3051.01 3011.5 L3073.93 3011.5 L3073.93 2689.49 L3051.01 2689.49 L3051.01 2689.49  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="3051.01,2689.49 3051.01,3011.5 3073.93,3011.5 3073.93,2689.49 3051.01,2689.49 "/>
<path clip-path="url(#clip003)" d="M3091.13 2850.5 L3091.13 3011.5 L3114.05 3011.5 L3114.05 2850.5 L3091.13 2850.5 L3091.13 2850.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="3091.13,2850.5 3091.13,3011.5 3114.05,3011.5 3114.05,2850.5 3091.13,2850.5 "/>
<path clip-path="url(#clip003)" d="M3131.24 2850.5 L3131.24 3011.5 L3154.16 3011.5 L3154.16 2850.5 L3131.24 2850.5 L3131.24 2850.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="3131.24,2850.5 3131.24,3011.5 3154.16,3011.5 3154.16,2850.5 3131.24,2850.5 "/>
<path clip-path="url(#clip003)" d="M3171.36 3011.5 L3171.36 3011.5 L3194.28 3011.5 L3194.28 3011.5 L3171.36 3011.5 L3171.36 3011.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="3171.36,3011.5 3171.36,3011.5 3194.28,3011.5 3171.36,3011.5 "/>
<path clip-path="url(#clip003)" d="M3211.47 2850.5 L3211.47 3011.5 L3234.39 3011.5 L3234.39 2850.5 L3211.47 2850.5 L3211.47 2850.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="3211.47,2850.5 3211.47,3011.5 3234.39,3011.5 3234.39,2850.5 3211.47,2850.5 "/>
<path clip-path="url(#clip003)" d="M3251.59 2850.5 L3251.59 3011.5 L3274.51 3011.5 L3274.51 2850.5 L3251.59 2850.5 L3251.59 2850.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="3251.59,2850.5 3251.59,3011.5 3274.51,3011.5 3274.51,2850.5 3251.59,2850.5 "/>
<path clip-path="url(#clip003)" d="M3291.7 3011.5 L3291.7 3011.5 L3314.62 3011.5 L3314.62 3011.5 L3291.7 3011.5 L3291.7 3011.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="3291.7,3011.5 3291.7,3011.5 3314.62,3011.5 3291.7,3011.5 "/>
<path clip-path="url(#clip003)" d="M3331.82 3011.5 L3331.82 3011.5 L3354.74 3011.5 L3354.74 3011.5 L3331.82 3011.5 L3331.82 3011.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="3331.82,3011.5 3331.82,3011.5 3354.74,3011.5 3331.82,3011.5 "/>
<path clip-path="url(#clip003)" d="M3371.93 3011.5 L3371.93 3011.5 L3394.85 3011.5 L3394.85 3011.5 L3371.93 3011.5 L3371.93 3011.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="3371.93,3011.5 3371.93,3011.5 3394.85,3011.5 3371.93,3011.5 "/>
<path clip-path="url(#clip003)" d="M3412.05 2850.5 L3412.05 3011.5 L3434.97 3011.5 L3434.97 2850.5 L3412.05 2850.5 L3412.05 2850.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="3412.05,2850.5 3412.05,3011.5 3434.97,3011.5 3434.97,2850.5 3412.05,2850.5 "/>
<path clip-path="url(#clip003)" d="M3452.16 3011.5 L3452.16 3011.5 L3475.08 3011.5 L3475.08 3011.5 L3452.16 3011.5 L3452.16 3011.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="3452.16,3011.5 3452.16,3011.5 3475.08,3011.5 3452.16,3011.5 "/>
<path clip-path="url(#clip003)" d="M3492.28 2850.5 L3492.28 3011.5 L3515.2 3011.5 L3515.2 2850.5 L3492.28 2850.5 L3492.28 2850.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="3492.28,2850.5 3492.28,3011.5 3515.2,3011.5 3515.2,2850.5 3492.28,2850.5 "/>
<path clip-path="url(#clip003)" d="M3532.39 2850.5 L3532.39 3011.5 L3555.31 3011.5 L3555.31 2850.5 L3532.39 2850.5 L3532.39 2850.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="3532.39,2850.5 3532.39,3011.5 3555.31,3011.5 3555.31,2850.5 3532.39,2850.5 "/>
<path clip-path="url(#clip003)" d="M3572.51 3011.5 L3572.51 3011.5 L3595.43 3011.5 L3595.43 3011.5 L3572.51 3011.5 L3572.51 3011.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="3572.51,3011.5 3572.51,3011.5 3595.43,3011.5 3572.51,3011.5 "/>
<path clip-path="url(#clip003)" d="M3612.62 3011.5 L3612.62 3011.5 L3635.54 3011.5 L3635.54 3011.5 L3612.62 3011.5 L3612.62 3011.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="3612.62,3011.5 3612.62,3011.5 3635.54,3011.5 3612.62,3011.5 "/>
<path clip-path="url(#clip003)" d="M3652.74 2689.49 L3652.74 3011.5 L3675.66 3011.5 L3675.66 2689.49 L3652.74 2689.49 L3652.74 2689.49  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="3652.74,2689.49 3652.74,3011.5 3675.66,3011.5 3675.66,2689.49 3652.74,2689.49 "/>
<path clip-path="url(#clip003)" d="M3692.85 3011.5 L3692.85 3011.5 L3715.77 3011.5 L3715.77 3011.5 L3692.85 3011.5 L3692.85 3011.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="3692.85,3011.5 3692.85,3011.5 3715.77,3011.5 3692.85,3011.5 "/>
<path clip-path="url(#clip003)" d="M3732.97 3011.5 L3732.97 3011.5 L3755.89 3011.5 L3755.89 3011.5 L3732.97 3011.5 L3732.97 3011.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="3732.97,3011.5 3732.97,3011.5 3755.89,3011.5 3732.97,3011.5 "/>
<path clip-path="url(#clip003)" d="M3773.08 2367.49 L3773.08 3011.5 L3796.01 3011.5 L3796.01 2367.49 L3773.08 2367.49 L3773.08 2367.49  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="3773.08,2367.49 3773.08,3011.5 3796.01,3011.5 3796.01,2367.49 3773.08,2367.49 "/>
<path clip-path="url(#clip003)" d="M3813.2 2850.5 L3813.2 3011.5 L3836.12 3011.5 L3836.12 2850.5 L3813.2 2850.5 L3813.2 2850.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="3813.2,2850.5 3813.2,3011.5 3836.12,3011.5 3836.12,2850.5 3813.2,2850.5 "/>
<path clip-path="url(#clip003)" d="M3853.31 3011.5 L3853.31 3011.5 L3876.24 3011.5 L3876.24 3011.5 L3853.31 3011.5 L3853.31 3011.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="3853.31,3011.5 3853.31,3011.5 3876.24,3011.5 3853.31,3011.5 "/>
<path clip-path="url(#clip003)" d="M3893.43 2689.49 L3893.43 3011.5 L3916.35 3011.5 L3916.35 2689.49 L3893.43 2689.49 L3893.43 2689.49  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="3893.43,2689.49 3893.43,3011.5 3916.35,3011.5 3916.35,2689.49 3893.43,2689.49 "/>
<path clip-path="url(#clip003)" d="M3933.54 2850.5 L3933.54 3011.5 L3956.47 3011.5 L3956.47 2850.5 L3933.54 2850.5 L3933.54 2850.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="3933.54,2850.5 3933.54,3011.5 3956.47,3011.5 3956.47,2850.5 3933.54,2850.5 "/>
<path clip-path="url(#clip003)" d="M3973.66 3011.5 L3973.66 3011.5 L3996.58 3011.5 L3996.58 3011.5 L3973.66 3011.5 L3973.66 3011.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="3973.66,3011.5 3973.66,3011.5 3996.58,3011.5 3973.66,3011.5 "/>
<path clip-path="url(#clip003)" d="M4013.77 3011.5 L4013.77 3011.5 L4036.7 3011.5 L4036.7 3011.5 L4013.77 3011.5 L4013.77 3011.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="4013.77,3011.5 4013.77,3011.5 4036.7,3011.5 4013.77,3011.5 "/>
<path clip-path="url(#clip003)" d="M4053.89 2528.49 L4053.89 3011.5 L4076.81 3011.5 L4076.81 2528.49 L4053.89 2528.49 L4053.89 2528.49  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="4053.89,2528.49 4053.89,3011.5 4076.81,3011.5 4076.81,2528.49 4053.89,2528.49 "/>
<path clip-path="url(#clip003)" d="M4094 3011.5 L4094 3011.5 L4116.93 3011.5 L4116.93 3011.5 L4094 3011.5 L4094 3011.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="4094,3011.5 4094,3011.5 4116.93,3011.5 4094,3011.5 "/>
<path clip-path="url(#clip003)" d="M4134.12 3011.5 L4134.12 3011.5 L4157.04 3011.5 L4157.04 3011.5 L4134.12 3011.5 L4134.12 3011.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="4134.12,3011.5 4134.12,3011.5 4157.04,3011.5 4134.12,3011.5 "/>
<path clip-path="url(#clip003)" d="M4174.23 2850.5 L4174.23 3011.5 L4197.16 3011.5 L4197.16 2850.5 L4174.23 2850.5 L4174.23 2850.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="4174.23,2850.5 4174.23,3011.5 4197.16,3011.5 4197.16,2850.5 4174.23,2850.5 "/>
<path clip-path="url(#clip003)" d="M4214.35 3011.5 L4214.35 3011.5 L4237.27 3011.5 L4237.27 3011.5 L4214.35 3011.5 L4214.35 3011.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="4214.35,3011.5 4214.35,3011.5 4237.27,3011.5 4214.35,3011.5 "/>
<path clip-path="url(#clip003)" d="M4254.46 2850.5 L4254.46 3011.5 L4277.39 3011.5 L4277.39 2850.5 L4254.46 2850.5 L4254.46 2850.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="4254.46,2850.5 4254.46,3011.5 4277.39,3011.5 4277.39,2850.5 4254.46,2850.5 "/>
<path clip-path="url(#clip003)" d="M4294.58 2850.5 L4294.58 3011.5 L4317.5 3011.5 L4317.5 2850.5 L4294.58 2850.5 L4294.58 2850.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="4294.58,2850.5 4294.58,3011.5 4317.5,3011.5 4317.5,2850.5 4294.58,2850.5 "/>
<path clip-path="url(#clip003)" d="M4334.69 2850.5 L4334.69 3011.5 L4357.62 3011.5 L4357.62 2850.5 L4334.69 2850.5 L4334.69 2850.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="4334.69,2850.5 4334.69,3011.5 4357.62,3011.5 4357.62,2850.5 4334.69,2850.5 "/>
<path clip-path="url(#clip003)" d="M4374.81 2689.49 L4374.81 3011.5 L4397.73 3011.5 L4397.73 2689.49 L4374.81 2689.49 L4374.81 2689.49  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="4374.81,2689.49 4374.81,3011.5 4397.73,3011.5 4397.73,2689.49 4374.81,2689.49 "/>
<path clip-path="url(#clip003)" d="M4414.92 3011.5 L4414.92 3011.5 L4437.85 3011.5 L4437.85 3011.5 L4414.92 3011.5 L4414.92 3011.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="4414.92,3011.5 4414.92,3011.5 4437.85,3011.5 4414.92,3011.5 "/>
<path clip-path="url(#clip003)" d="M4455.04 2850.5 L4455.04 3011.5 L4477.96 3011.5 L4477.96 2850.5 L4455.04 2850.5 L4455.04 2850.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="4455.04,2850.5 4455.04,3011.5 4477.96,3011.5 4477.96,2850.5 4455.04,2850.5 "/>
<path clip-path="url(#clip003)" d="M4483.69 3011.5 L4483.69 3011.5 L4506.62 3011.5 L4506.62 3011.5 L4483.69 3011.5 L4483.69 3011.5  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip003)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:0.7; fill:none" points="4483.69,3011.5 4483.69,3011.5 4506.62,3011.5 4483.69,3011.5 "/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="535.221" cy="2850.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="575.336" cy="3011.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="615.451" cy="2850.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="655.566" cy="3011.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="695.681" cy="2850.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="735.796" cy="3011.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="775.912" cy="2850.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="816.027" cy="2850.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="856.142" cy="2689.49" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="896.257" cy="3011.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="936.372" cy="2528.49" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="976.487" cy="2850.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="1016.6" cy="2689.49" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="1056.72" cy="2850.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="1096.83" cy="2689.49" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="1136.95" cy="2689.49" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="1177.06" cy="3011.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="1217.18" cy="3011.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="1257.29" cy="2528.49" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="1297.41" cy="2850.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="1337.52" cy="3011.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="1377.64" cy="2850.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="1417.75" cy="3011.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="1457.87" cy="2850.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="1497.98" cy="2689.49" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="1538.1" cy="3011.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="1578.21" cy="2850.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="1618.33" cy="2850.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="1658.44" cy="2850.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="1698.56" cy="3011.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="1738.67" cy="2689.49" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="1778.79" cy="2850.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="1818.9" cy="3011.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="1859.02" cy="3011.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="1899.13" cy="2850.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="1939.25" cy="3011.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="1979.36" cy="3011.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="2019.48" cy="3011.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="2059.59" cy="3011.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="2099.71" cy="2689.49" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="2139.82" cy="2850.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="2179.94" cy="3011.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="2220.05" cy="2689.49" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="2260.17" cy="2850.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="2300.29" cy="2850.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="2340.4" cy="3011.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="2380.52" cy="2850.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="2420.63" cy="2850.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="2460.75" cy="2689.49" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="2500.86" cy="3011.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="2540.98" cy="3011.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="2581.09" cy="3011.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="2621.21" cy="2850.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="2661.32" cy="3011.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="2701.44" cy="2528.49" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="2741.55" cy="3011.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="2781.67" cy="3011.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="2821.78" cy="2689.49" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="2861.9" cy="2850.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="2902.01" cy="2850.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="2942.13" cy="2689.49" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="2982.24" cy="2850.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="3022.36" cy="2850.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="3062.47" cy="2689.49" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="3102.59" cy="2850.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="3142.7" cy="2850.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="3182.82" cy="3011.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="3222.93" cy="2850.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="3263.05" cy="2850.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="3303.16" cy="3011.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="3343.28" cy="3011.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="3383.39" cy="3011.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="3423.51" cy="2850.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="3463.62" cy="3011.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="3503.74" cy="2850.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="3543.85" cy="2850.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="3583.97" cy="3011.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="3624.08" cy="3011.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="3664.2" cy="2689.49" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="3704.31" cy="3011.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="3744.43" cy="3011.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="3784.54" cy="2367.49" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="3824.66" cy="2850.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="3864.77" cy="3011.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="3904.89" cy="2689.49" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="3945" cy="2850.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="3985.12" cy="3011.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="4025.23" cy="3011.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="4065.35" cy="2528.49" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="4105.46" cy="3011.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="4145.58" cy="3011.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="4185.69" cy="2850.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="4225.81" cy="3011.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="4265.92" cy="2850.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="4306.04" cy="2850.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="4346.16" cy="2850.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="4386.27" cy="2689.49" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="4426.39" cy="3011.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="4466.5" cy="2850.5" r="2"/>
<circle clip-path="url(#clip003)" style="fill:#3366b2; stroke:none; fill-opacity:0" cx="4495.15" cy="3011.5" r="2"/>
<path clip-path="url(#clip000)" d="M426.79 2492.63 L954.905 2492.63 L954.905 2388.95 L426.79 2388.95  Z" fill="#ffffff" fill-rule="evenodd" fill-opacity="1"/>
<polyline clip-path="url(#clip000)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:1; fill:none" points="426.79,2492.63 954.905,2492.63 954.905,2388.95 426.79,2388.95 426.79,2492.63 "/>
<path clip-path="url(#clip000)" d="M476.514 2461.53 L774.856 2461.53 L774.856 2420.06 L476.514 2420.06 L476.514 2461.53  Z" fill="#3366b2" fill-rule="evenodd" fill-opacity="0.7"/>
<polyline clip-path="url(#clip000)" style="stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:1; fill:none" points="476.514,2461.53 774.856,2461.53 774.856,2420.06 476.514,2420.06 476.514,2461.53 "/>
<path clip-path="url(#clip000)" d="M824.58 2466.45 L824.58 2424.65 L848.284 2424.65 L848.284 2466.45 L824.58 2466.45 M827.242 2463.81 L845.645 2463.81 L845.645 2427.31 L827.242 2427.31 L827.242 2463.81 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M853.029 2466.45 L853.029 2424.65 L876.733 2424.65 L876.733 2466.45 L853.029 2466.45 M855.691 2463.81 L874.094 2463.81 L874.094 2427.31 L855.691 2427.31 L855.691 2463.81 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /><path clip-path="url(#clip000)" d="M881.478 2466.45 L881.478 2424.65 L905.182 2424.65 L905.182 2466.45 L881.478 2466.45 M884.14 2463.81 L902.543 2463.81 L902.543 2427.31 L884.14 2427.31 L884.14 2463.81 Z" fill="#000000" fill-rule="nonzero" fill-opacity="1" /></svg>
