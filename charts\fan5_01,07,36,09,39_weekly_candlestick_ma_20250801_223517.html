<script src="https://cdn.plot.ly/plotly-2.6.3.min.js"></script>    <div id="fe25a0dc_d567_48ae_ac67_ec6365f92863" style="width:1200px;height:800px;"></div>
    <script>
    function plots_jl_plotly_fe25a0dc_d567_48ae_ac67_ec6365f92863() {
        
        Plotly.newPlot('fe25a0dc_d567_48ae_ac67_ec6365f92863', [
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738773.0,
            738773.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            27.341733269501415,
            29.23564505541862
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738773.0,
            738773.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            27.99999999999998,
            28.85714285714284
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738780.0,
            738780.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            26.688811937185516,
            28.440107146545188
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738780.0,
            738780.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            26.99999999999998,
            27.857142857142836
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738787.0,
            738787.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            25.58159183536124,
            27.66772477056001
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738787.0,
            738787.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            25.99999999999998,
            26.857142857142836
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738794.0,
            738794.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            24.287493083733477,
            27.062341002315943
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738794.0,
            738794.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            24.99999999999998,
            25.857142857142836
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738801.0,
            738801.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            24.26037822548636,
            27.479674204024146
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738801.0,
            738801.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            24.857142857142836,
            26.99999999999998
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738808.0,
            738808.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            25.794609088303744,
            27.925123169487602
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738808.0,
            738808.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            25.99999999999998,
            26.857142857142836
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738815.0,
            738815.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            25.041223639591347,
            29.17558351846309
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738815.0,
            738815.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            25.857142857142836,
            27.99999999999998
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738822.0,
            738822.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            27.17364141074283,
            28.452083964432166
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738822.0,
            738822.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            27.857142857142836,
            27.999999999999982
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738829.0,
            738829.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            26.155732133824845,
            29.943430971469212
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738829.0,
            738829.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            27.85714285714284,
            28.999999999999982
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738836.0,
            738836.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            27.745924279854112,
            29.282429386930446
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738836.0,
            738836.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            27.999999999999982,
            28.85714285714284
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738843.0,
            738843.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            27.196157911214787,
            29.845455013111135
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738843.0,
            738843.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            27.85714285714284,
            28.99999999999998
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738850.0,
            738850.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            28.29809584258923,
            29.950736644537937
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738850.0,
            738850.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            28.99999999999998,
            29.85714285714284
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738857.0,
            738857.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            27.09471874448572,
            29.16761218546491
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738857.0,
            738857.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            27.99999999999998,
            28.857142857142836
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738864.0,
            738864.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            26.087562187172136,
            28.65852681250071
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738864.0,
            738864.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            26.99999999999998,
            27.857142857142836
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738871.0,
            738871.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            25.97187185962735,
            28.572127305393202
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738871.0,
            738871.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            26.857142857142836,
            27.999999999999982
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738878.0,
            738878.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            26.05713791055555,
            28.445404527159553
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738878.0,
            738878.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            26.999999999999982,
            27.85714285714284
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738885.0,
            738885.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            25.312151077347522,
            27.342337142132738
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738885.0,
            738885.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            25.999999999999982,
            26.85714285714284
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738892.0,
            738892.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            25.20281559588914,
            27.74900282465847
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738892.0,
            738892.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            25.85714285714284,
            25.999999999999982
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738899.0,
            738899.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            24.281307967182986,
            26.703112080691387
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738899.0,
            738899.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            24.999999999999982,
            25.85714285714284
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738906.0,
            738906.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            23.861671130951525,
            25.37398470507389
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738906.0,
            738906.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            23.999999999999982,
            24.85714285714284
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738913.0,
            738913.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            22.631956647181433,
            24.882570907659
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738913.0,
            738913.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            22.99999999999998,
            23.85714285714284
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738920.0,
            738920.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            21.60458748467303,
            23.917615261556527
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738920.0,
            738920.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            22.857142857142836,
            22.99999999999998
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738927.0,
            738927.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            20.850033596711164,
            23.12715330293298
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738927.0,
            738927.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            21.999999999999975,
            22.857142857142836
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738934.0,
            738934.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            20.796776825107802,
            22.1086698507151
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738934.0,
            738934.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            20.99999999999997,
            21.857142857142833
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738941.0,
            738941.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            20.18352488259926,
            21.72121788899063
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738941.0,
            738941.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            20.85714285714283,
            20.99999999999997
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738948.0,
            738948.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            20.31713265985806,
            22.164957991882147
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738948.0,
            738948.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            20.857142857142826,
            20.999999999999968
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738955.0,
            738955.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            19.326014348176013,
            21.478645166485027
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738955.0,
            738955.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            20.857142857142826,
            20.99999999999997
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738962.0,
            738962.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            20.435099545377785,
            22.23627608815727
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738962.0,
            738962.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            20.857142857142826,
            20.99999999999997
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738969.0,
            738969.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            21.6571289714715,
            23.72333216577176
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738969.0,
            738969.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            21.857142857142826,
            22.99999999999997
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738976.0,
            738976.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            22.215834140965566,
            23.663088813933513
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738976.0,
            738976.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            22.85714285714283,
            22.99999999999997
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738983.0,
            738983.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            21.741230618636475,
            23.56269904912263
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738983.0,
            738983.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            22.85714285714283,
            22.99999999999997
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738990.0,
            738990.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            22.00674489738567,
            24.156678438868163
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738990.0,
            738990.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            22.85714285714283,
            23.99999999999997
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738997.0,
            738997.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            23.028200741356713,
            25.076063387644012
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738997.0,
            738997.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            23.85714285714283,
            23.99999999999997
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739004.0,
            739004.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            22.616557133419665,
            24.370330011754575
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739004.0,
            739004.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            22.99999999999997,
            23.85714285714283
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739011.0,
            739011.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            21.69219678906022,
            25.0910073200986
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739011.0,
            739011.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            22.85714285714283,
            24.99999999999997
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739018.0,
            739018.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            23.59710969671718,
            25.8546339505187
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739018.0,
            739018.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            24.85714285714283,
            24.999999999999975
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739025.0,
            739025.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            23.592588398893234,
            25.206061366378837
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739025.0,
            739025.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            23.999999999999975,
            24.857142857142833
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739032.0,
            739032.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            22.04269138808246,
            24.191287760420376
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739032.0,
            739032.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            22.999999999999975,
            23.857142857142833
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739039.0,
            739039.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            22.014100250658647,
            24.209962330715328
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739039.0,
            739039.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            22.857142857142833,
            22.999999999999975
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739046.0,
            739046.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            22.564427410752184,
            24.551240878110097
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739046.0,
            739046.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            22.999999999999975,
            23.857142857142833
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739053.0,
            739053.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            21.66899804244311,
            22.920618722837208
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739053.0,
            739053.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            21.99999999999998,
            22.857142857142833
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739060.0,
            739060.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            20.685502203648326,
            22.28026050605743
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739060.0,
            739060.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            20.99999999999998,
            21.857142857142833
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739067.0,
            739067.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            19.815111353061,
            21.79902278792929
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739067.0,
            739067.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            20.857142857142833,
            20.999999999999975
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739074.0,
            739074.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            19.201711540950985,
            21.600632754808384
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739074.0,
            739074.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            19.999999999999975,
            20.857142857142833
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739081.0,
            739081.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            19.248160367502393,
            21.0359782681026
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739081.0,
            739081.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            19.857142857142833,
            19.99999999999997
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739088.0,
            739088.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            18.72669990757386,
            20.04171607831195
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739088.0,
            739088.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            18.999999999999968,
            19.85714285714283
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739095.0,
            739095.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            18.445684641894434,
            22.701673865627985
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739095.0,
            739095.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            19.857142857142826,
            21.999999999999968
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739102.0,
            739102.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            20.151531023092776,
            22.506539739812798
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739102.0,
            739102.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            20.999999999999968,
            21.857142857142826
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739109.0,
            739109.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            20.452173586967834,
            21.957355463970746
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739109.0,
            739109.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            20.999999999999968,
            21.857142857142826
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739116.0,
            739116.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            19.51112137275864,
            21.727580426970924
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739116.0,
            739116.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            20.857142857142826,
            20.999999999999968
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739123.0,
            739123.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            19.630609500741112,
            21.57599068257772
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739123.0,
            739123.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            19.999999999999968,
            20.857142857142826
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739130.0,
            739130.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            18.62670353527523,
            20.134855192301075
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739130.0,
            739130.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            18.999999999999968,
            19.857142857142826
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739137.0,
            739137.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            18.236883237387026,
            19.840543277370283
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739137.0,
            739137.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            18.857142857142826,
            18.999999999999968
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739144.0,
            739144.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            19.676803817178016,
            22.11197949392092
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739144.0,
            739144.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            19.857142857142826,
            20.999999999999968
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739151.0,
            739151.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            20.304711688717855,
            22.041274867142047
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739151.0,
            739151.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            20.857142857142826,
            20.999999999999968
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739158.0,
            739158.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            19.80560278730222,
            20.985531883071634
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739158.0,
            739158.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            19.99999999999997,
            20.857142857142826
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739165.0,
            739165.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            18.807380279071456,
            20.003596610488014
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739165.0,
            739165.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            18.99999999999997,
            19.857142857142826
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739172.0,
            739172.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            18.195026776160006,
            20.734819422940053
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739172.0,
            739172.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            18.857142857142826,
            19.999999999999968
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739179.0,
            739179.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            19.15969765971291,
            21.690567503326246
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739179.0,
            739179.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            19.857142857142826,
            20.99999999999997
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739186.0,
            739186.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            20.582339934587583,
            24.182881627368157
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739186.0,
            739186.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            20.857142857142826,
            22.99999999999997
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739193.0,
            739193.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            22.30013838761915,
            24.009194214775878
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739193.0,
            739193.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            22.857142857142826,
            22.999999999999968
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739200.0,
            739200.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            22.315278476725652,
            24.155821608299256
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739200.0,
            739200.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            22.857142857142826,
            22.999999999999968
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739207.0,
            739207.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            22.336605349468385,
            24.371452106276053
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739207.0,
            739207.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            22.999999999999964,
            23.857142857142826
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739214.0,
            739214.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            22.40214798261861,
            24.465808159138902
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739214.0,
            739214.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            22.857142857142822,
            23.999999999999964
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739221.0,
            739221.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            22.94032806622086,
            25.06428714221341
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739221.0,
            739221.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            23.85714285714282,
            23.99999999999996
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739228.0,
            739228.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            21.875913874041974,
            24.553808762832215
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739228.0,
            739228.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            22.999999999999957,
            23.85714285714282
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739235.0,
            739235.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            21.867780111327853,
            24.16085354163486
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739235.0,
            739235.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            22.857142857142815,
            22.999999999999957
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739242.0,
            739242.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            22.462479781531478,
            24.648579384748775
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739242.0,
            739242.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            23.857142857142815,
            23.999999999999957
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739249.0,
            739249.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            23.338599774728834,
            25.369964650758014
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739249.0,
            739249.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            23.857142857142815,
            23.999999999999957
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739256.0,
            739256.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            23.656083630117454,
            26.415110376635084
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739256.0,
            739256.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            24.857142857142815,
            24.999999999999957
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739263.0,
            739263.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            24.731349328742052,
            26.32127579159484
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739263.0,
            739263.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            25.857142857142815,
            25.999999999999957
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739270.0,
            739270.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            24.7181629741807,
            26.154576904059887
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739270.0,
            739270.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            24.999999999999957,
            25.857142857142815
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739277.0,
            739277.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            23.417972032362677,
            25.056418212806328
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739277.0,
            739277.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            23.999999999999957,
            24.857142857142815
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739284.0,
            739284.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            22.616117822702563,
            24.284432427491197
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739284.0,
            739284.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            22.999999999999957,
            23.857142857142815
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739291.0,
            739291.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            22.1404752151625,
            24.085868483278908
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739291.0,
            739291.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            22.999999999999957,
            23.857142857142815
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739298.0,
            739298.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            21.03723491558268,
            23.040425394171855
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739298.0,
            739298.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            21.999999999999957,
            22.857142857142815
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739305.0,
            739305.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            20.7727950796031,
            22.87899305283091
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739305.0,
            739305.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            20.99999999999996,
            21.857142857142815
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739312.0,
            739312.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            19.523591725494438,
            21.101011046531106
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739312.0,
            739312.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            19.99999999999996,
            20.85714285714282
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739319.0,
            739319.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            20.298427262460102,
            22.049677279533157
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739319.0,
            739319.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            20.85714285714282,
            20.999999999999957
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739326.0,
            739326.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            20.811264785878063,
            22.61998714000609
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739326.0,
            739326.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            21.857142857142815,
            21.999999999999957
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739333.0,
            739333.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            20.50250735721828,
            22.541611651033733
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739333.0,
            739333.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            20.999999999999957,
            21.857142857142815
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739340.0,
            739340.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            21.314160847522402,
            23.40232442483776
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739340.0,
            739340.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            21.857142857142815,
            22.99999999999996
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739347.0,
            739347.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            21.705920469669003,
            23.285584771340506
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739347.0,
            739347.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            21.99999999999996,
            22.85714285714282
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739354.0,
            739354.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            20.75475460913511,
            23.0226984438764
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739354.0,
            739354.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            21.85714285714282,
            21.99999999999996
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739361.0,
            739361.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            20.898854444523984,
            23.89758567728306
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739361.0,
            739361.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            21.85714285714282,
            22.99999999999996
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739368.0,
            739368.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            21.56720696681136,
            23.135096157854854
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739368.0,
            739368.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            21.99999999999996,
            22.85714285714282
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739375.0,
            739375.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            20.595333416268527,
            22.40777206773762
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739375.0,
            739375.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            20.99999999999996,
            21.85714285714282
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739382.0,
            739382.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            19.505967471743435,
            21.540424301175587
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739382.0,
            739382.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            19.99999999999996,
            20.85714285714282
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739389.0,
            739389.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            19.804482662550583,
            27.03956562817817
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739389.0,
            739389.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            19.85714285714282,
            25.99999999999996
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739396.0,
            739396.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            24.103319288686546,
            26.77045259306014
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739396.0,
            739396.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            24.99999999999996,
            25.85714285714282
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739403.0,
            739403.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            23.16929804461972,
            25.60990198180519
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739403.0,
            739403.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            23.99999999999996,
            24.85714285714282
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739410.0,
            739410.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            22.296432386377,
            24.54600992968237
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739410.0,
            739410.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            22.99999999999996,
            23.85714285714282
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739417.0,
            739417.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            21.784951577305495,
            23.553388929638995
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739417.0,
            739417.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            21.99999999999996,
            22.85714285714282
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739424.0,
            739424.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            20.91657104242545,
            22.532136285779426
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739424.0,
            739424.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            20.99999999999996,
            21.85714285714282
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739431.0,
            739431.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            19.7108461133626,
            21.412612374518847
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739431.0,
            739431.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            20.85714285714282,
            20.999999999999957
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739438.0,
            739438.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            19.769450997025743,
            21.525295524659693
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739438.0,
            739438.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            19.999999999999957,
            20.857142857142815
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739445.0,
            739445.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            19.018759732661614,
            21.50645809729121
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739445.0,
            739445.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            19.85714285714281,
            20.999999999999954
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739452.0,
            739452.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            20.106255210981264,
            21.791068469151863
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739452.0,
            739452.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            20.85714285714281,
            20.99999999999995
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739459.0,
            739459.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            19.675329883730335,
            21.096380229641806
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739459.0,
            739459.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(0, 128, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            19.99999999999995,
            20.857142857142808
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739464.0,
            739464.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.700)",
            "shape": "linear",
            "dash": "solid",
            "width": 1
        },
        "y": [
            19.420364259589736,
            20.524595371344393
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            739464.0,
            739464.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(255, 0, 0, 0.800)",
            "shape": "linear",
            "dash": "solid",
            "width": 4
        },
        "y": [
            19.857142857142804,
            20.28571428571423
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738801.0,
            738808.0,
            738815.0,
            738822.0,
            738829.0,
            738836.0,
            738843.0,
            738850.0,
            738857.0,
            738864.0,
            738871.0,
            738878.0,
            738885.0,
            738892.0,
            738899.0,
            738906.0,
            738913.0,
            738920.0,
            738927.0,
            738934.0,
            738941.0,
            738948.0,
            738955.0,
            738962.0,
            738969.0,
            738976.0,
            738983.0,
            738990.0,
            738997.0,
            739004.0,
            739011.0,
            739018.0,
            739025.0,
            739032.0,
            739039.0,
            739046.0,
            739053.0,
            739060.0,
            739067.0,
            739074.0,
            739081.0,
            739088.0,
            739095.0,
            739102.0,
            739109.0,
            739116.0,
            739123.0,
            739130.0,
            739137.0,
            739144.0,
            739151.0,
            739158.0,
            739165.0,
            739172.0,
            739179.0,
            739186.0,
            739193.0,
            739200.0,
            739207.0,
            739214.0,
            739221.0,
            739228.0,
            739235.0,
            739242.0,
            739249.0,
            739256.0,
            739263.0,
            739270.0,
            739277.0,
            739284.0,
            739291.0,
            739298.0,
            739305.0,
            739312.0,
            739319.0,
            739326.0,
            739333.0,
            739340.0,
            739347.0,
            739354.0,
            739361.0,
            739368.0,
            739375.0,
            739382.0,
            739389.0,
            739396.0,
            739403.0,
            739410.0,
            739417.0,
            739424.0,
            739431.0,
            739438.0,
            739445.0,
            739452.0,
            739459.0,
            739464.0
        ],
        "showlegend": true,
        "mode": "lines",
        "name": "MA5",
        "legendgroup": "MA5",
        "line": {
            "color": "rgba(0, 0, 255, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 2
        },
        "y": [
            26.599999999999977,
            26.199999999999978,
            26.399999999999977,
            26.799999999999976,
            27.599999999999984,
            27.799999999999983,
            28.399999999999984,
            28.599999999999977,
            28.599999999999977,
            28.199999999999978,
            28.199999999999978,
            27.799999999999983,
            27.19999999999998,
            26.799999999999983,
            26.399999999999984,
            25.599999999999984,
            24.799999999999983,
            24.199999999999978,
            23.399999999999977,
            22.599999999999977,
            21.999999999999975,
            21.599999999999973,
            21.19999999999997,
            20.99999999999997,
            21.39999999999997,
            21.799999999999972,
            22.19999999999997,
            22.799999999999972,
            23.39999999999997,
            23.39999999999997,
            23.799999999999972,
            24.19999999999997,
            24.19999999999997,
            23.99999999999997,
            23.99999999999997,
            23.599999999999973,
            22.99999999999998,
            22.399999999999977,
            21.99999999999998,
            21.399999999999977,
            20.799999999999976,
            20.199999999999974,
            20.39999999999997,
            20.39999999999997,
            20.599999999999973,
            20.79999999999997,
            20.999999999999968,
            20.39999999999997,
            19.999999999999968,
            19.999999999999968,
            19.999999999999968,
            19.999999999999968,
            19.999999999999968,
            20.19999999999997,
            20.19999999999997,
            20.599999999999973,
            21.19999999999997,
            21.99999999999997,
            22.599999999999973,
            23.19999999999997,
            23.399999999999963,
            23.39999999999996,
            23.39999999999996,
            23.59999999999996,
            23.59999999999996,
            23.799999999999958,
            24.399999999999956,
            24.799999999999958,
            24.799999999999958,
            24.59999999999996,
            24.199999999999957,
            23.399999999999956,
            22.59999999999996,
            21.799999999999958,
            21.399999999999956,
            21.199999999999957,
            20.999999999999957,
            21.399999999999956,
            21.799999999999958,
            21.999999999999957,
            22.199999999999957,
            22.39999999999996,
            21.99999999999996,
            21.59999999999996,
            22.39999999999996,
            22.79999999999996,
            23.19999999999996,
            23.59999999999996,
            23.99999999999996,
            22.99999999999996,
            22.19999999999996,
            21.39999999999996,
            20.999999999999957,
            20.799999999999955,
            20.599999999999955,
            20.45714285714281
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738836.0,
            738843.0,
            738850.0,
            738857.0,
            738864.0,
            738871.0,
            738878.0,
            738885.0,
            738892.0,
            738899.0,
            738906.0,
            738913.0,
            738920.0,
            738927.0,
            738934.0,
            738941.0,
            738948.0,
            738955.0,
            738962.0,
            738969.0,
            738976.0,
            738983.0,
            738990.0,
            738997.0,
            739004.0,
            739011.0,
            739018.0,
            739025.0,
            739032.0,
            739039.0,
            739046.0,
            739053.0,
            739060.0,
            739067.0,
            739074.0,
            739081.0,
            739088.0,
            739095.0,
            739102.0,
            739109.0,
            739116.0,
            739123.0,
            739130.0,
            739137.0,
            739144.0,
            739151.0,
            739158.0,
            739165.0,
            739172.0,
            739179.0,
            739186.0,
            739193.0,
            739200.0,
            739207.0,
            739214.0,
            739221.0,
            739228.0,
            739235.0,
            739242.0,
            739249.0,
            739256.0,
            739263.0,
            739270.0,
            739277.0,
            739284.0,
            739291.0,
            739298.0,
            739305.0,
            739312.0,
            739319.0,
            739326.0,
            739333.0,
            739340.0,
            739347.0,
            739354.0,
            739361.0,
            739368.0,
            739375.0,
            739382.0,
            739389.0,
            739396.0,
            739403.0,
            739410.0,
            739417.0,
            739424.0,
            739431.0,
            739438.0,
            739445.0,
            739452.0,
            739459.0,
            739464.0
        ],
        "showlegend": true,
        "mode": "lines",
        "name": "MA10",
        "legendgroup": "MA10",
        "line": {
            "color": "rgba(255, 165, 0, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 2
        },
        "y": [
            27.199999999999978,
            27.299999999999976,
            27.49999999999998,
            27.699999999999978,
            27.899999999999977,
            27.99999999999998,
            28.099999999999977,
            27.899999999999977,
            27.699999999999978,
            27.299999999999976,
            26.899999999999977,
            26.299999999999976,
            25.699999999999978,
            25.099999999999977,
            24.49999999999998,
            23.799999999999976,
            23.199999999999978,
            22.699999999999974,
            22.199999999999974,
            21.999999999999975,
            21.89999999999997,
            21.89999999999997,
            21.99999999999997,
            22.19999999999997,
            22.39999999999997,
            22.799999999999972,
            23.19999999999997,
            23.49999999999997,
            23.69999999999997,
            23.69999999999997,
            23.69999999999997,
            23.599999999999973,
            23.299999999999972,
            22.99999999999997,
            22.69999999999997,
            22.199999999999974,
            21.599999999999973,
            21.399999999999974,
            21.199999999999974,
            20.999999999999975,
            20.799999999999972,
            20.599999999999973,
            20.39999999999997,
            20.19999999999997,
            20.299999999999972,
            20.39999999999997,
            20.499999999999968,
            20.199999999999967,
            20.09999999999997,
            20.09999999999997,
            20.29999999999997,
            20.59999999999997,
            20.999999999999968,
            21.39999999999997,
            21.69999999999997,
            21.99999999999997,
            22.299999999999972,
            22.699999999999967,
            23.099999999999966,
            23.39999999999996,
            23.599999999999955,
            23.899999999999956,
            24.099999999999955,
            24.199999999999953,
            24.099999999999955,
            23.999999999999954,
            23.899999999999956,
            23.699999999999957,
            23.29999999999996,
            22.99999999999996,
            22.69999999999996,
            22.199999999999953,
            21.999999999999957,
            21.79999999999996,
            21.699999999999964,
            21.699999999999964,
            21.699999999999964,
            21.699999999999964,
            21.699999999999964,
            22.199999999999964,
            22.499999999999964,
            22.799999999999965,
            22.799999999999965,
            22.799999999999965,
            22.699999999999967,
            22.499999999999964,
            22.29999999999996,
            22.29999999999996,
            22.399999999999956,
            21.799999999999955,
            21.328571428571383
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.9967191601049868
        },
        "yaxis": "y1",
        "x": [
            738906.0,
            738913.0,
            738920.0,
            738927.0,
            738934.0,
            738941.0,
            738948.0,
            738955.0,
            738962.0,
            738969.0,
            738976.0,
            738983.0,
            738990.0,
            738997.0,
            739004.0,
            739011.0,
            739018.0,
            739025.0,
            739032.0,
            739039.0,
            739046.0,
            739053.0,
            739060.0,
            739067.0,
            739074.0,
            739081.0,
            739088.0,
            739095.0,
            739102.0,
            739109.0,
            739116.0,
            739123.0,
            739130.0,
            739137.0,
            739144.0,
            739151.0,
            739158.0,
            739165.0,
            739172.0,
            739179.0,
            739186.0,
            739193.0,
            739200.0,
            739207.0,
            739214.0,
            739221.0,
            739228.0,
            739235.0,
            739242.0,
            739249.0,
            739256.0,
            739263.0,
            739270.0,
            739277.0,
            739284.0,
            739291.0,
            739298.0,
            739305.0,
            739312.0,
            739319.0,
            739326.0,
            739333.0,
            739340.0,
            739347.0,
            739354.0,
            739361.0,
            739368.0,
            739375.0,
            739382.0,
            739389.0,
            739396.0,
            739403.0,
            739410.0,
            739417.0,
            739424.0,
            739431.0,
            739438.0,
            739445.0,
            739452.0,
            739459.0,
            739464.0
        ],
        "showlegend": true,
        "mode": "lines",
        "name": "MA20",
        "legendgroup": "MA20",
        "line": {
            "color": "rgba(128, 0, 128, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 2
        },
        "y": [
            27.049999999999983,
            26.799999999999983,
            26.599999999999984,
            26.399999999999984,
            26.199999999999978,
            25.899999999999977,
            25.649999999999977,
            25.299999999999976,
            24.949999999999978,
            24.649999999999984,
            24.399999999999984,
            24.099999999999984,
            23.849999999999977,
            23.649999999999977,
            23.449999999999978,
            23.299999999999976,
            23.199999999999978,
            23.099999999999977,
            22.949999999999978,
            22.849999999999977,
            22.799999999999976,
            22.74999999999998,
            22.649999999999974,
            22.599999999999973,
            22.549999999999976,
            22.49999999999997,
            22.39999999999997,
            22.449999999999967,
            22.449999999999967,
            22.34999999999997,
            22.249999999999968,
            22.09999999999997,
            21.849999999999973,
            21.59999999999997,
            21.499999999999968,
            21.299999999999972,
            21.049999999999972,
            20.799999999999972,
            20.64999999999997,
            20.549999999999972,
            20.549999999999972,
            20.59999999999997,
            20.699999999999967,
            20.799999999999965,
            20.999999999999968,
            21.199999999999967,
            21.399999999999967,
            21.449999999999967,
            21.599999999999966,
            21.749999999999964,
            21.949999999999964,
            22.249999999999964,
            22.54999999999996,
            22.79999999999996,
            22.89999999999996,
            22.99999999999996,
            23.09999999999996,
            23.19999999999996,
            23.19999999999996,
            23.199999999999957,
            23.149999999999956,
            23.049999999999958,
            23.049999999999958,
            22.999999999999957,
            22.899999999999956,
            22.849999999999955,
            22.799999999999955,
            22.699999999999953,
            22.499999999999954,
            22.599999999999955,
            22.599999999999955,
            22.499999999999954,
            22.399999999999956,
            22.299999999999958,
            22.199999999999953,
            22.099999999999955,
            21.999999999999957,
            21.999999999999957,
            22.04999999999996,
            21.99999999999996,
            21.914285714285672
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738771.0,
            738771.0,
            738775.0,
            738775.0,
            738771.0,
            738771.0
        ],
        "showlegend": true,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738778.0,
            738778.0,
            738782.0,
            738782.0,
            738778.0,
            738778.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738785.0,
            738785.0,
            738789.0,
            738789.0,
            738785.0,
            738785.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738792.0,
            738792.0,
            738796.0,
            738796.0,
            738792.0,
            738792.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738799.0,
            738799.0,
            738803.0,
            738803.0,
            738799.0,
            738799.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            3.0,
            0.0,
            0.0,
            3.0,
            3.0,
            3.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738806.0,
            738806.0,
            738810.0,
            738810.0,
            738806.0,
            738806.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738813.0,
            738813.0,
            738817.0,
            738817.0,
            738813.0,
            738813.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            3.0,
            0.0,
            0.0,
            3.0,
            3.0,
            3.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738820.0,
            738820.0,
            738824.0,
            738824.0,
            738820.0,
            738820.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738827.0,
            738827.0,
            738831.0,
            738831.0,
            738827.0,
            738827.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            2.0,
            0.0,
            0.0,
            2.0,
            2.0,
            2.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738834.0,
            738834.0,
            738838.0,
            738838.0,
            738834.0,
            738834.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738841.0,
            738841.0,
            738845.0,
            738845.0,
            738841.0,
            738841.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            2.0,
            0.0,
            0.0,
            2.0,
            2.0,
            2.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738848.0,
            738848.0,
            738852.0,
            738852.0,
            738848.0,
            738848.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738855.0,
            738855.0,
            738859.0,
            738859.0,
            738855.0,
            738855.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738862.0,
            738862.0,
            738866.0,
            738866.0,
            738862.0,
            738862.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738869.0,
            738869.0,
            738873.0,
            738873.0,
            738869.0,
            738869.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            2.0,
            0.0,
            0.0,
            2.0,
            2.0,
            2.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738876.0,
            738876.0,
            738880.0,
            738880.0,
            738876.0,
            738876.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738883.0,
            738883.0,
            738887.0,
            738887.0,
            738883.0,
            738883.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738890.0,
            738890.0,
            738894.0,
            738894.0,
            738890.0,
            738890.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738897.0,
            738897.0,
            738901.0,
            738901.0,
            738897.0,
            738897.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738904.0,
            738904.0,
            738908.0,
            738908.0,
            738904.0,
            738904.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738911.0,
            738911.0,
            738915.0,
            738915.0,
            738911.0,
            738911.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738918.0,
            738918.0,
            738922.0,
            738922.0,
            738918.0,
            738918.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738925.0,
            738925.0,
            738929.0,
            738929.0,
            738925.0,
            738925.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738932.0,
            738932.0,
            738936.0,
            738936.0,
            738932.0,
            738932.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738939.0,
            738939.0,
            738943.0,
            738943.0,
            738939.0,
            738939.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738946.0,
            738946.0,
            738950.0,
            738950.0,
            738946.0,
            738946.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738953.0,
            738953.0,
            738957.0,
            738957.0,
            738953.0,
            738953.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738960.0,
            738960.0,
            738964.0,
            738964.0,
            738960.0,
            738960.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738967.0,
            738967.0,
            738971.0,
            738971.0,
            738967.0,
            738967.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            3.0,
            0.0,
            0.0,
            3.0,
            3.0,
            3.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738974.0,
            738974.0,
            738978.0,
            738978.0,
            738974.0,
            738974.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738981.0,
            738981.0,
            738985.0,
            738985.0,
            738981.0,
            738981.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738988.0,
            738988.0,
            738992.0,
            738992.0,
            738988.0,
            738988.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            2.0,
            0.0,
            0.0,
            2.0,
            2.0,
            2.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738995.0,
            738995.0,
            738999.0,
            738999.0,
            738995.0,
            738995.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739002.0,
            739002.0,
            739006.0,
            739006.0,
            739002.0,
            739002.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739009.0,
            739009.0,
            739013.0,
            739013.0,
            739009.0,
            739009.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            3.0,
            0.0,
            0.0,
            3.0,
            3.0,
            3.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739016.0,
            739016.0,
            739020.0,
            739020.0,
            739016.0,
            739016.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739023.0,
            739023.0,
            739027.0,
            739027.0,
            739023.0,
            739023.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739030.0,
            739030.0,
            739034.0,
            739034.0,
            739030.0,
            739030.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739037.0,
            739037.0,
            739041.0,
            739041.0,
            739037.0,
            739037.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739044.0,
            739044.0,
            739048.0,
            739048.0,
            739044.0,
            739044.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739051.0,
            739051.0,
            739055.0,
            739055.0,
            739051.0,
            739051.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739058.0,
            739058.0,
            739062.0,
            739062.0,
            739058.0,
            739058.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739065.0,
            739065.0,
            739069.0,
            739069.0,
            739065.0,
            739065.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739072.0,
            739072.0,
            739076.0,
            739076.0,
            739072.0,
            739072.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739079.0,
            739079.0,
            739083.0,
            739083.0,
            739079.0,
            739079.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739086.0,
            739086.0,
            739090.0,
            739090.0,
            739086.0,
            739086.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739093.0,
            739093.0,
            739097.0,
            739097.0,
            739093.0,
            739093.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            4.0,
            0.0,
            0.0,
            4.0,
            4.0,
            4.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739100.0,
            739100.0,
            739104.0,
            739104.0,
            739100.0,
            739100.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739107.0,
            739107.0,
            739111.0,
            739111.0,
            739107.0,
            739107.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739114.0,
            739114.0,
            739118.0,
            739118.0,
            739114.0,
            739114.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739121.0,
            739121.0,
            739125.0,
            739125.0,
            739121.0,
            739121.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739128.0,
            739128.0,
            739132.0,
            739132.0,
            739128.0,
            739128.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739135.0,
            739135.0,
            739139.0,
            739139.0,
            739135.0,
            739135.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739142.0,
            739142.0,
            739146.0,
            739146.0,
            739142.0,
            739142.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            3.0,
            0.0,
            0.0,
            3.0,
            3.0,
            3.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739149.0,
            739149.0,
            739153.0,
            739153.0,
            739149.0,
            739149.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739156.0,
            739156.0,
            739160.0,
            739160.0,
            739156.0,
            739156.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739163.0,
            739163.0,
            739167.0,
            739167.0,
            739163.0,
            739163.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739170.0,
            739170.0,
            739174.0,
            739174.0,
            739170.0,
            739170.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            2.0,
            0.0,
            0.0,
            2.0,
            2.0,
            2.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739177.0,
            739177.0,
            739181.0,
            739181.0,
            739177.0,
            739177.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            2.0,
            0.0,
            0.0,
            2.0,
            2.0,
            2.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739184.0,
            739184.0,
            739188.0,
            739188.0,
            739184.0,
            739184.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            3.0,
            0.0,
            0.0,
            3.0,
            3.0,
            3.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739191.0,
            739191.0,
            739195.0,
            739195.0,
            739191.0,
            739191.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739198.0,
            739198.0,
            739202.0,
            739202.0,
            739198.0,
            739198.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739205.0,
            739205.0,
            739209.0,
            739209.0,
            739205.0,
            739205.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739212.0,
            739212.0,
            739216.0,
            739216.0,
            739212.0,
            739212.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            2.0,
            0.0,
            0.0,
            2.0,
            2.0,
            2.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739219.0,
            739219.0,
            739223.0,
            739223.0,
            739219.0,
            739219.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739226.0,
            739226.0,
            739230.0,
            739230.0,
            739226.0,
            739226.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739233.0,
            739233.0,
            739237.0,
            739237.0,
            739233.0,
            739233.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739240.0,
            739240.0,
            739244.0,
            739244.0,
            739240.0,
            739240.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            2.0,
            0.0,
            0.0,
            2.0,
            2.0,
            2.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739247.0,
            739247.0,
            739251.0,
            739251.0,
            739247.0,
            739247.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739254.0,
            739254.0,
            739258.0,
            739258.0,
            739254.0,
            739254.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            2.0,
            0.0,
            0.0,
            2.0,
            2.0,
            2.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739261.0,
            739261.0,
            739265.0,
            739265.0,
            739261.0,
            739261.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            2.0,
            0.0,
            0.0,
            2.0,
            2.0,
            2.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739268.0,
            739268.0,
            739272.0,
            739272.0,
            739268.0,
            739268.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739275.0,
            739275.0,
            739279.0,
            739279.0,
            739275.0,
            739275.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739282.0,
            739282.0,
            739286.0,
            739286.0,
            739282.0,
            739282.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739289.0,
            739289.0,
            739293.0,
            739293.0,
            739289.0,
            739289.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739296.0,
            739296.0,
            739300.0,
            739300.0,
            739296.0,
            739296.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739303.0,
            739303.0,
            739307.0,
            739307.0,
            739303.0,
            739303.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739310.0,
            739310.0,
            739314.0,
            739314.0,
            739310.0,
            739310.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739317.0,
            739317.0,
            739321.0,
            739321.0,
            739317.0,
            739317.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            2.0,
            0.0,
            0.0,
            2.0,
            2.0,
            2.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739324.0,
            739324.0,
            739328.0,
            739328.0,
            739324.0,
            739324.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            2.0,
            0.0,
            0.0,
            2.0,
            2.0,
            2.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739331.0,
            739331.0,
            739335.0,
            739335.0,
            739331.0,
            739331.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739338.0,
            739338.0,
            739342.0,
            739342.0,
            739338.0,
            739338.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            3.0,
            0.0,
            0.0,
            3.0,
            3.0,
            3.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739345.0,
            739345.0,
            739349.0,
            739349.0,
            739345.0,
            739345.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739352.0,
            739352.0,
            739356.0,
            739356.0,
            739352.0,
            739352.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739359.0,
            739359.0,
            739363.0,
            739363.0,
            739359.0,
            739359.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            2.0,
            0.0,
            0.0,
            2.0,
            2.0,
            2.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739366.0,
            739366.0,
            739370.0,
            739370.0,
            739366.0,
            739366.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739373.0,
            739373.0,
            739377.0,
            739377.0,
            739373.0,
            739373.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739380.0,
            739380.0,
            739384.0,
            739384.0,
            739380.0,
            739380.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739387.0,
            739387.0,
            739391.0,
            739391.0,
            739387.0,
            739387.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            7.0,
            0.0,
            0.0,
            7.0,
            7.0,
            7.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739394.0,
            739394.0,
            739398.0,
            739398.0,
            739394.0,
            739394.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739401.0,
            739401.0,
            739405.0,
            739405.0,
            739401.0,
            739401.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739408.0,
            739408.0,
            739412.0,
            739412.0,
            739408.0,
            739408.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739415.0,
            739415.0,
            739419.0,
            739419.0,
            739415.0,
            739415.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739422.0,
            739422.0,
            739426.0,
            739426.0,
            739422.0,
            739422.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739429.0,
            739429.0,
            739433.0,
            739433.0,
            739429.0,
            739429.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739436.0,
            739436.0,
            739440.0,
            739440.0,
            739436.0,
            739436.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739443.0,
            739443.0,
            739447.0,
            739447.0,
            739443.0,
            739443.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            2.0,
            0.0,
            0.0,
            2.0,
            2.0,
            2.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739450.0,
            739450.0,
            739454.0,
            739454.0,
            739450.0,
            739450.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739457.0,
            739457.0,
            739461.0,
            739461.0,
            739457.0,
            739457.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739462.0,
            739462.0,
            739466.0,
            739466.0,
            739462.0,
            739462.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(70, 130, 180, 0.600)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.600)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "colorbar": {
            "y": 0.14547278269903766,
            "title": "",
            "len": 0.21513082349081364,
            "x": 0.9967191601049868
        },
        "yaxis": "y2",
        "x": [
            738773.0,
            738780.0,
            738787.0,
            738794.0,
            738801.0,
            738808.0,
            738815.0,
            738822.0,
            738829.0,
            738836.0,
            738843.0,
            738850.0,
            738857.0,
            738864.0,
            738871.0,
            738878.0,
            738885.0,
            738892.0,
            738899.0,
            738906.0,
            738913.0,
            738920.0,
            738927.0,
            738934.0,
            738941.0,
            738948.0,
            738955.0,
            738962.0,
            738969.0,
            738976.0,
            738983.0,
            738990.0,
            738997.0,
            739004.0,
            739011.0,
            739018.0,
            739025.0,
            739032.0,
            739039.0,
            739046.0,
            739053.0,
            739060.0,
            739067.0,
            739074.0,
            739081.0,
            739088.0,
            739095.0,
            739102.0,
            739109.0,
            739116.0,
            739123.0,
            739130.0,
            739137.0,
            739144.0,
            739151.0,
            739158.0,
            739165.0,
            739172.0,
            739179.0,
            739186.0,
            739193.0,
            739200.0,
            739207.0,
            739214.0,
            739221.0,
            739228.0,
            739235.0,
            739242.0,
            739249.0,
            739256.0,
            739263.0,
            739270.0,
            739277.0,
            739284.0,
            739291.0,
            739298.0,
            739305.0,
            739312.0,
            739319.0,
            739326.0,
            739333.0,
            739340.0,
            739347.0,
            739354.0,
            739361.0,
            739368.0,
            739375.0,
            739382.0,
            739389.0,
            739396.0,
            739403.0,
            739410.0,
            739417.0,
            739424.0,
            739431.0,
            739438.0,
            739445.0,
            739452.0,
            739459.0,
            739464.0
        ],
        "showlegend": false,
        "mode": "markers",
        "name": "成交量",
        "legendgroup": "成交量",
        "marker": {
            "symbol": "circle",
            "color": "rgba(70, 130, 180, 0.000)",
            "line": {
                "color": "rgba(0, 0, 0, 0.000)",
                "width": 1
            },
            "size": 0
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            3.0,
            0.0,
            3.0,
            1.0,
            2.0,
            0.0,
            2.0,
            1.0,
            0.0,
            0.0,
            2.0,
            0.0,
            0.0,
            1.0,
            0.0,
            0.0,
            0.0,
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0,
            1.0,
            3.0,
            1.0,
            1.0,
            2.0,
            1.0,
            0.0,
            3.0,
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            0.0,
            0.0,
            1.0,
            0.0,
            1.0,
            0.0,
            4.0,
            0.0,
            1.0,
            1.0,
            0.0,
            0.0,
            1.0,
            3.0,
            1.0,
            0.0,
            0.0,
            2.0,
            2.0,
            3.0,
            1.0,
            1.0,
            1.0,
            2.0,
            1.0,
            0.0,
            1.0,
            2.0,
            1.0,
            2.0,
            2.0,
            0.0,
            0.0,
            0.0,
            1.0,
            0.0,
            0.0,
            0.0,
            2.0,
            2.0,
            0.0,
            3.0,
            0.0,
            1.0,
            2.0,
            0.0,
            0.0,
            0.0,
            7.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            1.0,
            0.0,
            2.0,
            1.0,
            0.0,
            1.0
        ],
        "type": "scatter"
    }
]
, {
    "showlegend": true,
    "paper_bgcolor": "rgba(255, 255, 255, 1.000)",
    "xaxis1": {
        "showticklabels": true,
        "gridwidth": 1,
        "tickvals": [
            738886.0,
            739252.0
        ],
        "range": [
            738752.27,
            739484.73
        ],
        "domain": [
            0.05420676582093905,
            0.9967191601049868
        ],
        "mirror": false,
        "tickangle": 0,
        "showline": true,
        "ticktext": [
            "2024-01-01",
            "2025-01-01"
        ],
        "zeroline": false,
        "tickfont": {
            "color": "rgba(0, 0, 0, 1.000)",
            "family": "Microsoft YaHei, SimHei, Arial Unicode MS, sans-serif",
            "size": 14
        },
        "zerolinecolor": "rgba(0, 0, 0, 1.000)",
        "anchor": "y1",
        "visible": true,
        "ticks": "inside",
        "tickmode": "array",
        "linecolor": "rgba(0, 0, 0, 1.000)",
        "showgrid": true,
        "title": "日期",
        "gridcolor": "rgba(211, 211, 211, 0.100)",
        "titlefont": {
            "color": "rgba(0, 0, 0, 1.000)",
            "family": "Microsoft YaHei, SimHei, Arial Unicode MS, sans-serif",
            "size": 17
        },
        "tickcolor": "rgb(0, 0, 0)",
        "type": "-"
    },
    "legend": {
        "yanchor": "auto",
        "xanchor": "auto",
        "bordercolor": "rgba(0, 0, 0, 1.000)",
        "bgcolor": "rgba(255, 255, 255, 1.000)",
        "borderwidth": 1,
        "tracegroupgap": 0,
        "y": 1.0,
        "font": {
            "color": "rgba(0, 0, 0, 1.000)",
            "family": "Microsoft YaHei, SimHei, Arial Unicode MS, sans-serif",
            "size": 15
        },
        "title": {
            "font": {
                "color": "rgba(0, 0, 0, 1.000)",
                "family": "Microsoft YaHei, SimHei, Arial Unicode MS, sans-serif",
                "size": 15
            },
            "text": ""
        },
        "traceorder": "normal",
        "x": 1.0
    },
    "height": 800,
    "yaxis2": {
        "showticklabels": true,
        "gridwidth": 0.5,
        "tickvals": [
            0.0,
            1.0,
            2.0,
            3.0,
            4.0,
            5.0,
            6.0,
            7.0
        ],
        "range": [
            0.0,
            7.0
        ],
        "domain": [
            0.03790737095363082,
            0.2530381944444445
        ],
        "mirror": false,
        "tickangle": 0,
        "showline": true,
        "ticktext": [
            "0",
            "1",
            "2",
            "3",
            "4",
            "5",
            "6",
            "7"
        ],
        "zeroline": false,
        "tickfont": {
            "color": "rgba(0, 0, 0, 1.000)",
            "family": "Microsoft YaHei, SimHei, Arial Unicode MS, sans-serif",
            "size": 13
        },
        "zerolinecolor": "rgba(0, 0, 0, 1.000)",
        "anchor": "x2",
        "visible": true,
        "ticks": "inside",
        "tickmode": "array",
        "linecolor": "rgba(0, 0, 0, 1.000)",
        "showgrid": true,
        "title": "成交量",
        "gridcolor": "rgba(0, 0, 0, 0.100)",
        "titlefont": {
            "color": "rgba(0, 0, 0, 1.000)",
            "family": "Microsoft YaHei, SimHei, Arial Unicode MS, sans-serif",
            "size": 14
        },
        "tickcolor": "rgb(0, 0, 0)",
        "type": "-"
    },
    "xaxis2": {
        "showticklabels": true,
        "gridwidth": 0.5,
        "tickvals": [
            738886.0,
            739252.0
        ],
        "range": [
            738728.049,
            739508.951
        ],
        "domain": [
            0.05420676582093905,
            0.9967191601049868
        ],
        "mirror": false,
        "tickangle": 0,
        "showline": true,
        "ticktext": [
            "2024-01-01",
            "2025-01-01"
        ],
        "zeroline": false,
        "tickfont": {
            "color": "rgba(0, 0, 0, 1.000)",
            "family": "Microsoft YaHei, SimHei, Arial Unicode MS, sans-serif",
            "size": 13
        },
        "zerolinecolor": "rgba(0, 0, 0, 1.000)",
        "anchor": "y2",
        "visible": true,
        "ticks": "inside",
        "tickmode": "array",
        "linecolor": "rgba(0, 0, 0, 1.000)",
        "showgrid": true,
        "title": "日期",
        "gridcolor": "rgba(0, 0, 0, 0.100)",
        "titlefont": {
            "color": "rgba(0, 0, 0, 1.000)",
            "family": "Microsoft YaHei, SimHei, Arial Unicode MS, sans-serif",
            "size": 14
        },
        "tickcolor": "rgb(0, 0, 0)",
        "type": "-"
    },
    "yaxis1": {
        "showticklabels": true,
        "gridwidth": 1,
        "tickvals": [
            20.0,
            22.5,
            25.0,
            27.5,
            30.0
        ],
        "range": [
            17.84235548010867,
            30.303407940589278
        ],
        "domain": [
            0.3219084919072616,
            0.9673009623797024
        ],
        "mirror": false,
        "tickangle": 0,
        "showline": true,
        "ticktext": [
            "20.0",
            "22.5",
            "25.0",
            "27.5",
            "30.0"
        ],
        "zeroline": false,
        "tickfont": {
            "color": "rgba(0, 0, 0, 1.000)",
            "family": "Microsoft YaHei, SimHei, Arial Unicode MS, sans-serif",
            "size": 14
        },
        "zerolinecolor": "rgba(0, 0, 0, 1.000)",
        "anchor": "x1",
        "visible": true,
        "ticks": "inside",
        "tickmode": "array",
        "linecolor": "rgba(0, 0, 0, 1.000)",
        "showgrid": true,
        "title": "價格",
        "gridcolor": "rgba(211, 211, 211, 0.100)",
        "titlefont": {
            "color": "rgba(0, 0, 0, 1.000)",
            "family": "Microsoft YaHei, SimHei, Arial Unicode MS, sans-serif",
            "size": 17
        },
        "tickcolor": "rgb(0, 0, 0)",
        "type": "-"
    },
    "annotations": [
        {
            "yanchor": "top",
            "xanchor": "center",
            "rotation": -0.0,
            "y": 1.0,
            "font": {
                "color": "rgba(0, 0, 0, 1.000)",
                "family": "Microsoft YaHei, SimHei, Arial Unicode MS, sans-serif",
                "size": 22
            },
            "yref": "paper",
            "showarrow": false,
            "text": "fan5_01,07,36,09,39 週線 蠟燭圖+MA",
            "xref": "paper",
            "x": 0.525462962962963
        },
        {
            "yanchor": "top",
            "xanchor": "center",
            "rotation": -0.0,
            "y": 0.27879278762029747,
            "font": {
                "color": "rgba(0, 0, 0, 1.000)",
                "family": "Microsoft YaHei, SimHei, Arial Unicode MS, sans-serif",
                "size": 17
            },
            "yref": "paper",
            "showarrow": false,
            "text": "成交量",
            "xref": "paper",
            "x": 0.525462962962963
        }
    ],
    "plot_bgcolor": "rgba(255, 255, 255, 1.000)",
    "margin": {
        "l": 0,
        "b": 20,
        "r": 0,
        "t": 20
    },
    "width": 1200
}
);
        
    }
    </script>
    <script src="https://requirejs.org/docs/release/2.3.7/minified/require.js" onload="plots_jl_plotly_fe25a0dc_d567_48ae_ac67_ec6365f92863()"></script>
