#!/usr/bin/env julia

"""
測試 PlotlyJS 後端是否正常工作
"""

println("🧪 測試 PlotlyJS 後端...")

# 測試 PlotlyJS 是否可用
println("\n1. 測試 PlotlyJS 包載入...")
try
    using PlotlyJS
    println("✅ PlotlyJS 包載入成功")
catch e
    println("❌ PlotlyJS 包載入失敗：$(e)")
    println("請安裝：julia --project=. -e \"using Pkg; Pkg.add(\\\"PlotlyJS\\\")\"")
    exit(1)
end

# 測試 Plots.jl 與 PlotlyJS 後端
println("\n2. 測試 Plots.jl 與 PlotlyJS 後端...")
try
    using Plots
    plotlyjs()
    println("✅ Plots.jl 已切換到 PlotlyJS 後端")
    
    # 測試基本繪圖
    println("\n3. 測試基本繪圖功能...")
    x = 1:10
    y = rand(10)
    p = plot(x, y, title="測試圖表", xlabel="X軸", ylabel="Y軸")
    println("✅ 基本繪圖測試成功")
    
    # 測試中文字體設置
    println("\n4. 測試中文字體設置...")
    if Sys.iswindows()
        font_family = "Microsoft YaHei, SimHei, Arial Unicode MS, sans-serif"
    elseif Sys.islinux()
        font_family = "WenQuanYi Micro Hei, Noto Sans CJK SC, DejaVu Sans, sans-serif"
    elseif Sys.isapple()
        font_family = "PingFang SC, Hiragino Sans GB, Arial Unicode MS, sans-serif"
    else
        font_family = "Arial Unicode MS, sans-serif"
    end
    
    default(
        fontfamily=font_family,
        titlefontsize=16,
        guidefontsize=12,
        tickfontsize=10,
        legendfontsize=11
    )
    println("✅ 中文字體設置完成：$font_family")
    
    # 測試中文圖表
    println("\n5. 測試中文圖表生成...")
    p_chinese = plot(x, y, 
                    title="中文測試圖表", 
                    xlabel="時間軸", 
                    ylabel="數值軸",
                    label="測試數據")
    
    # 保存測試圖表
    test_file = "test_chinese_chart.html"
    savefig(p_chinese, test_file)
    println("✅ 中文圖表生成成功，已保存為：$test_file")
    
    # 檢查文件中的中文
    if isfile(test_file)
        content = read(test_file, String)
        chinese_chars = ["中文測試圖表", "時間軸", "數值軸", "測試數據"]
        found_chars = filter(char -> occursin(char, content), chinese_chars)
        
        println("📄 中文字符檢測：$(join(found_chars, ", "))")
        
        if length(found_chars) == length(chinese_chars)
            println("🎉 PlotlyJS 中文顯示完全正常！")
        else
            missing_chars = setdiff(chinese_chars, found_chars)
            println("⚠️  部分中文字符缺失：$(join(missing_chars, ", "))")
        end
        
        # 清理測試文件
        rm(test_file)
    end
    
catch e
    println("❌ PlotlyJS 後端測試失敗：$(e)")
    println("詳細錯誤：")
    showerror(stdout, e, catch_backtrace())
end

println("\n🎯 結論：")
println("如果上述測試全部通過，說明 PlotlyJS 後端可以正常工作")
println("如果有錯誤，請檢查：")
println("1. PlotlyJS 是否正確安裝")
println("2. 系統是否有中文字體")
println("3. 網絡連接是否正常（PlotlyJS 需要下載資源）")

println("\n🎉 測試完成！")
