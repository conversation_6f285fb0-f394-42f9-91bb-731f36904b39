<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AmiBroker 圖表</title>
    <style>
        body {
            font-family: "Microsoft YaHei", "SimHei", "Arial Unicode MS", sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .chart-container {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
            margin: 0 auto;
            max-width: 1240px;
        }
        .chart-title {
            text-align: center;
            color: #333;
            margin-bottom: 20px;
            font-size: 24px;
            font-weight: bold;
        }
        .chart-info {
            text-align: center;
            color: #666;
            margin-bottom: 15px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="chart-container">
        <div class="chart-title">📊 AmiBroker 圖表模擬器</div>
        <div class="chart-info">🕯️ 專業蠟燭圖 | 漲紅跌綠 | 中文完美顯示</div>
        <script src="https://cdn.plot.ly/plotly-2.6.3.min.js"></script>    <div id="25f0076a_d3f3_4097_b200_51ab63c22344" style="width:1200px;height:800px;"></div>
    <script>
    function plots_jl_plotly_25f0076a_d3f3_4097_b200_51ab63c22344() {
        
        Plotly.newPlot('25f0076a_d3f3_4097_b200_51ab63c22344', [
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738773.0,
            738773.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -3.999999999999991,
            -3.1428571428571344
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738773.0,
            738773.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -3.999999999999991,
            -3.1428571428571344
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738780.0,
            738780.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -1.142857142857134,
            2.3247886973013836
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738780.0,
            738780.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -1.142857142857134,
            2.0000000000000093
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738787.0,
            738787.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            1.5155073927670217,
            3.1768670383122837
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738787.0,
            738787.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            1.8571428571428665,
            3.0000000000000098
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738794.0,
            738794.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            1.9705341563296093,
            3.0129071021800997
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738794.0,
            738794.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            2.0000000000000084,
            2.8571428571428665
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738801.0,
            738801.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            0.9949336615888663,
            1.908749674781758
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738801.0,
            738801.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            1.0000000000000087,
            1.8571428571428656
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738808.0,
            738808.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            8.829523866959576e-15,
            0.8641882739338387
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738808.0,
            738808.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            9.103828801926284e-15,
            0.8571428571428659
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738815.0,
            738815.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            9.43147112132035e-15,
            0.8683480805941849
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738815.0,
            738815.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            9.547918011776346e-15,
            0.8571428571428663
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738822.0,
            738822.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -0.9999999999999902,
            -0.14285714285713325
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738822.0,
            738822.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -0.9999999999999902,
            -0.14285714285713325
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738829.0,
            738829.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -1.9999999999999898,
            -1.142857142857133
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738829.0,
            738829.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -1.9999999999999898,
            -1.142857142857133
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738836.0,
            738836.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -2.428571428571419,
            -0.8571428571428474
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738836.0,
            738836.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -2.1428571428571326,
            -0.9999999999999902
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738843.0,
            738843.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -1.9999999999999898,
            -1.142857142857133
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738843.0,
            738843.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -1.9999999999999898,
            -1.142857142857133
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738850.0,
            738850.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -2.428571428571419,
            -1.5714285714285618
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738850.0,
            738850.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -2.1428571428571326,
            -1.9999999999999902
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738857.0,
            738857.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -2.999999999999992,
            -2.142857142857133
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738857.0,
            738857.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -2.999999999999992,
            -2.142857142857133
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738864.0,
            738864.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -3.1428571428571352,
            -2.2857142857142785
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738864.0,
            738864.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -3.1428571428571352,
            -2.9999999999999947
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738871.0,
            738871.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -3.999999999999997,
            -3.142857142857138
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738871.0,
            738871.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -3.999999999999997,
            -3.142857142857138
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738878.0,
            738878.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -4.9999999999999964,
            -4.14285714285714
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738878.0,
            738878.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -4.9999999999999964,
            -4.14285714285714
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738885.0,
            738885.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -5.14285714285714,
            -4.285714285714282
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738885.0,
            738885.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -5.14285714285714,
            -4.9999999999999964
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738892.0,
            738892.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -5.285714285714282,
            -4.428571428571424
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738892.0,
            738892.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -5.142857142857139,
            -4.999999999999996
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738899.0,
            738899.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -5.999999999999996,
            -5.142857142857139
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738899.0,
            738899.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -5.999999999999996,
            -5.142857142857139
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738906.0,
            738906.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -5.571428571428567,
            -4.71428571428571
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738906.0,
            738906.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -5.142857142857139,
            -4.999999999999995
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738913.0,
            738913.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -5.999999999999995,
            -5.142857142857138
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738913.0,
            738913.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -5.999999999999995,
            -5.142857142857138
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738920.0,
            738920.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -6.4285714285714235,
            -5.571428571428566
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738920.0,
            738920.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -6.142857142857137,
            -5.999999999999995
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738927.0,
            738927.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -5.571428571428566,
            -4.714285714285708
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738927.0,
            738927.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -5.142857142857137,
            -4.999999999999994
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738934.0,
            738934.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -5.999999999999993,
            -5.142857142857137
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738934.0,
            738934.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -5.999999999999993,
            -5.142857142857137
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738941.0,
            738941.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -6.285714285714279,
            -2.7142857142857073
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738941.0,
            738941.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -6.142857142857136,
            -2.999999999999993
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738948.0,
            738948.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -3.1428571428571357,
            -2.2857142857142785
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738948.0,
            738948.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -3.1428571428571357,
            -2.999999999999993
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738955.0,
            738955.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -3.1428571428571352,
            -2.2857142857142785
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738955.0,
            738955.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -3.1428571428571352,
            -2.9999999999999925
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738962.0,
            738962.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -3.999999999999992,
            -3.1428571428571352
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738962.0,
            738962.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -3.999999999999992,
            -3.1428571428571352
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738969.0,
            738969.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -4.999999999999991,
            -4.142857142857135
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738969.0,
            738969.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -4.999999999999991,
            -4.142857142857135
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738976.0,
            738976.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -5.999999999999991,
            -5.142857142857134
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738976.0,
            738976.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -5.999999999999991,
            -5.142857142857134
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738983.0,
            738983.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -6.999999999999993,
            -6.1428571428571335
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738983.0,
            738983.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -6.999999999999993,
            -6.1428571428571335
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738990.0,
            738990.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -7.999999999999995,
            -7.142857142857135
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738990.0,
            738990.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -7.999999999999995,
            -7.142857142857135
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738997.0,
            738997.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -8.999999999999998,
            -8.142857142857139
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            738997.0,
            738997.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -8.999999999999998,
            -8.142857142857139
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739004.0,
            739004.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -10.0,
            -9.14285714285714
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739004.0,
            739004.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -10.0,
            -9.14285714285714
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739011.0,
            739011.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -10.142857142857144,
            -7.571428571428571
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739011.0,
            739011.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -10.142857142857144,
            -8.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739018.0,
            739018.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -9.000000000000004,
            -8.142857142857144
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739018.0,
            739018.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -9.000000000000004,
            -8.142857142857144
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739025.0,
            739025.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -9.000000000000004,
            -8.142857142857146
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739025.0,
            739025.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -9.000000000000004,
            -8.142857142857146
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739032.0,
            739032.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -10.000000000000004,
            -9.142857142857146
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739032.0,
            739032.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -10.000000000000004,
            -9.142857142857146
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739039.0,
            739039.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -11.000000000000004,
            -10.142857142857146
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739039.0,
            739039.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -11.000000000000004,
            -10.142857142857146
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739046.0,
            739046.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -10.85714285714286,
            -8.000000000000002
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739046.0,
            739046.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -10.142857142857146,
            -8.000000000000002
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739053.0,
            739053.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -8.571428571428573,
            -7.714285714285716
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739053.0,
            739053.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -8.142857142857146,
            -8.000000000000002
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739060.0,
            739060.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -9.000000000000002,
            -8.142857142857144
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739060.0,
            739060.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -9.000000000000002,
            -8.142857142857144
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739067.0,
            739067.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -9.285714285714286,
            -6.428571428571431
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739067.0,
            739067.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -9.142857142857144,
            -7.000000000000002
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739074.0,
            739074.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -7.571428571428573,
            -6.714285714285715
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739074.0,
            739074.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -7.142857142857144,
            -7.000000000000001
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739081.0,
            739081.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -8.0,
            -7.142857142857144
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739081.0,
            739081.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -8.0,
            -7.142857142857144
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739088.0,
            739088.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -8.285714285714286,
            -7.428571428571429
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739088.0,
            739088.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -8.142857142857142,
            -8.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739095.0,
            739095.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -9.0,
            -8.142857142857142
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739095.0,
            739095.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -9.0,
            -8.142857142857142
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739102.0,
            739102.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -10.0,
            -9.142857142857142
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739102.0,
            739102.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -10.0,
            -9.142857142857142
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739109.0,
            739109.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -10.428571428571427,
            -8.714285714285714
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739109.0,
            739109.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -10.142857142857142,
            -8.999999999999998
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739116.0,
            739116.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -8.142857142857142,
            -7.285714285714285
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739116.0,
            739116.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -8.142857142857142,
            -7.999999999999998
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739123.0,
            739123.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -7.999999999999998,
            -7.1428571428571415
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739123.0,
            739123.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -7.999999999999998,
            -7.1428571428571415
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739130.0,
            739130.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -8.14285714285714,
            -4.428571428571427
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739130.0,
            739130.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -8.14285714285714,
            -4.999999999999998
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739137.0,
            739137.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -5.428571428571427,
            -4.571428571428569
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739137.0,
            739137.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -5.1428571428571415,
            -4.999999999999998
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739144.0,
            739144.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -5.999999999999997,
            -5.142857142857141
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739144.0,
            739144.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -5.999999999999997,
            -5.142857142857141
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739151.0,
            739151.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -6.9999999999999964,
            -6.142857142857141
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739151.0,
            739151.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -6.9999999999999964,
            -6.142857142857141
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739158.0,
            739158.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -7.9999999999999964,
            -7.14285714285714
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739158.0,
            739158.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -7.9999999999999964,
            -7.14285714285714
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739165.0,
            739165.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -7.428571428571425,
            -6.571428571428568
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739165.0,
            739165.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -7.142857142857139,
            -6.9999999999999964
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739172.0,
            739172.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -7.999999999999996,
            -7.142857142857139
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739172.0,
            739172.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -7.999999999999996,
            -7.142857142857139
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739179.0,
            739179.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -8.142857142857139,
            -7.285714285714281
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739179.0,
            739179.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -8.142857142857139,
            -7.999999999999995
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739186.0,
            739186.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -8.999999999999995,
            -8.142857142857139
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739186.0,
            739186.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -8.999999999999995,
            -8.142857142857139
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739193.0,
            739193.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -9.285714285714281,
            -7.714285714285708
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739193.0,
            739193.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -9.142857142857137,
            -7.999999999999995
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739200.0,
            739200.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -8.999999999999993,
            -8.142857142857137
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739200.0,
            739200.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -8.999999999999993,
            -8.142857142857137
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739207.0,
            739207.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -9.714285714285708,
            -8.85714285714285
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739207.0,
            739207.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -9.142857142857137,
            -8.999999999999993
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739214.0,
            739214.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -9.85714285714285,
            -8.999999999999993
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739214.0,
            739214.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -9.142857142857135,
            -8.999999999999993
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739221.0,
            739221.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -9.999999999999993,
            -9.142857142857135
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739221.0,
            739221.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -9.999999999999993,
            -9.142857142857135
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739228.0,
            739228.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -10.999999999999993,
            -10.142857142857135
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739228.0,
            739228.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -10.999999999999993,
            -10.142857142857135
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739235.0,
            739235.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -11.714285714285708,
            -10.85714285714285
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739235.0,
            739235.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -11.142857142857135,
            -10.999999999999993
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739242.0,
            739242.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -11.142857142857135,
            -9.571428571428564
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739242.0,
            739242.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -11.142857142857135,
            -9.999999999999993
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739249.0,
            739249.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -10.999999999999993,
            -10.142857142857137
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739249.0,
            739249.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -10.999999999999993,
            -10.142857142857137
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739256.0,
            739256.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -11.999999999999993,
            -11.142857142857135
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739256.0,
            739256.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -11.999999999999993,
            -11.142857142857135
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739263.0,
            739263.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -12.999999999999993,
            -12.142857142857135
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739263.0,
            739263.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -12.999999999999993,
            -12.142857142857135
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739270.0,
            739270.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -13.999999999999993,
            -13.142857142857135
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739270.0,
            739270.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -13.999999999999993,
            -13.142857142857135
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739277.0,
            739277.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -14.999999999999995,
            -14.142857142857135
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739277.0,
            739277.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -14.999999999999995,
            -14.142857142857135
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739284.0,
            739284.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -15.999999999999996,
            -15.142857142857139
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739284.0,
            739284.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -15.999999999999996,
            -15.142857142857139
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739291.0,
            739291.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -16.14285714285714,
            -15.285714285714283
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739291.0,
            739291.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -16.14285714285714,
            -16.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739298.0,
            739298.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -17.0,
            -16.142857142857142
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739298.0,
            739298.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -17.0,
            -16.142857142857142
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739305.0,
            739305.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -18.000000000000004,
            -17.142857142857146
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739305.0,
            739305.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -18.000000000000004,
            -17.142857142857146
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739312.0,
            739312.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -18.142857142857146,
            -14.714285714285717
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739312.0,
            739312.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -18.142857142857146,
            -15.000000000000004
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739319.0,
            739319.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -15.428571428571434,
            -14.571428571428575
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739319.0,
            739319.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -15.142857142857146,
            -15.000000000000004
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739326.0,
            739326.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -16.000000000000004,
            -15.142857142857146
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739326.0,
            739326.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -16.000000000000004,
            -15.142857142857146
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739333.0,
            739333.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -16.71428571428572,
            -15.857142857142861
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739333.0,
            739333.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -16.142857142857146,
            -16.000000000000004
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739340.0,
            739340.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -17.000000000000004,
            -16.142857142857146
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739340.0,
            739340.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -17.000000000000004,
            -16.142857142857146
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739347.0,
            739347.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -17.714285714285715,
            -16.000000000000004
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739347.0,
            739347.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -17.142857142857146,
            -16.000000000000004
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739354.0,
            739354.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -15.85714285714286,
            -15.000000000000002
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739354.0,
            739354.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -15.142857142857146,
            -15.000000000000002
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739361.0,
            739361.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -15.142857142857144,
            -14.285714285714288
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739361.0,
            739361.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -15.142857142857144,
            -15.000000000000002
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739368.0,
            739368.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -15.000000000000002,
            -14.142857142857144
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739368.0,
            739368.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -15.000000000000002,
            -14.142857142857144
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739375.0,
            739375.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -16.0,
            -15.142857142857144
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739375.0,
            739375.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -16.0,
            -15.142857142857144
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739382.0,
            739382.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -17.0,
            -16.142857142857146
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739382.0,
            739382.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -17.0,
            -16.142857142857146
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739389.0,
            739389.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -17.142857142857142,
            -15.857142857142858
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739389.0,
            739389.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -17.142857142857142,
            -16.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739396.0,
            739396.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -17.0,
            -16.142857142857142
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739396.0,
            739396.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -17.0,
            -16.142857142857142
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739403.0,
            739403.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -17.285714285714285,
            -16.428571428571427
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739403.0,
            739403.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -17.142857142857142,
            -17.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739410.0,
            739410.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -16.857142857142858,
            -16.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739410.0,
            739410.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -16.142857142857142,
            -16.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739417.0,
            739417.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -15.999999999999998,
            -15.142857142857142
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739417.0,
            739417.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -15.999999999999998,
            -15.142857142857142
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739424.0,
            739424.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -16.142857142857142,
            -12.857142857142856
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739424.0,
            739424.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -16.142857142857142,
            -12.999999999999998
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739431.0,
            739431.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -13.999999999999998,
            -13.142857142857142
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739431.0,
            739431.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -13.999999999999998,
            -13.142857142857142
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739438.0,
            739438.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -14.14285714285714,
            -10.714285714285712
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739438.0,
            739438.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -14.14285714285714,
            -10.999999999999998
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739445.0,
            739445.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -12.0,
            -11.142857142857142
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739445.0,
            739445.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(26, 153, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -12.0,
            -11.142857142857142
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739452.0,
            739452.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -12.285714285714286,
            -10.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739452.0,
            739452.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -12.142857142857142,
            -10.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739459.0,
            739459.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -10.42857142857143,
            -9.571428571428573
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739459.0,
            739459.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -10.142857142857142,
            -10.000000000000004
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739464.0,
            739464.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.900)",
            "shape": "linear",
            "dash": "solid",
            "width": 1.2
        },
        "y": [
            -10.142857142857146,
            -9.285714285714288
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x1",
        "colorbar": {
            "y": 0.644604727143482,
            "title": "",
            "len": 0.6453924704724407,
            "x": 0.996719160104987
        },
        "yaxis": "y1",
        "x": [
            739464.0,
            739464.0
        ],
        "showlegend": false,
        "mode": "lines",
        "name": "",
        "legendgroup": "",
        "line": {
            "color": "rgba(204, 26, 26, 0.850)",
            "shape": "linear",
            "dash": "solid",
            "width": 7
        },
        "y": [
            -10.142857142857146,
            -9.714285714285719
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738771.0,
            738771.0,
            738775.0,
            738775.0,
            738771.0,
            738771.0
        ],
        "showlegend": true,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738778.0,
            738778.0,
            738782.0,
            738782.0,
            738778.0,
            738778.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            7.0,
            0.0,
            0.0,
            7.0,
            7.0,
            7.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738785.0,
            738785.0,
            738789.0,
            738789.0,
            738785.0,
            738785.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            2.0,
            0.0,
            0.0,
            2.0,
            2.0,
            2.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738792.0,
            738792.0,
            738796.0,
            738796.0,
            738792.0,
            738792.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738799.0,
            738799.0,
            738803.0,
            738803.0,
            738799.0,
            738799.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738806.0,
            738806.0,
            738810.0,
            738810.0,
            738806.0,
            738806.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738813.0,
            738813.0,
            738817.0,
            738817.0,
            738813.0,
            738813.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738820.0,
            738820.0,
            738824.0,
            738824.0,
            738820.0,
            738820.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738827.0,
            738827.0,
            738831.0,
            738831.0,
            738827.0,
            738827.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738834.0,
            738834.0,
            738838.0,
            738838.0,
            738834.0,
            738834.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            2.0,
            0.0,
            0.0,
            2.0,
            2.0,
            2.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738841.0,
            738841.0,
            738845.0,
            738845.0,
            738841.0,
            738841.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738848.0,
            738848.0,
            738852.0,
            738852.0,
            738848.0,
            738848.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738855.0,
            738855.0,
            738859.0,
            738859.0,
            738855.0,
            738855.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738862.0,
            738862.0,
            738866.0,
            738866.0,
            738862.0,
            738862.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738869.0,
            738869.0,
            738873.0,
            738873.0,
            738869.0,
            738869.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738876.0,
            738876.0,
            738880.0,
            738880.0,
            738876.0,
            738876.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738883.0,
            738883.0,
            738887.0,
            738887.0,
            738883.0,
            738883.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738890.0,
            738890.0,
            738894.0,
            738894.0,
            738890.0,
            738890.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738897.0,
            738897.0,
            738901.0,
            738901.0,
            738897.0,
            738897.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738904.0,
            738904.0,
            738908.0,
            738908.0,
            738904.0,
            738904.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            2.0,
            0.0,
            0.0,
            2.0,
            2.0,
            2.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738911.0,
            738911.0,
            738915.0,
            738915.0,
            738911.0,
            738911.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738918.0,
            738918.0,
            738922.0,
            738922.0,
            738918.0,
            738918.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738925.0,
            738925.0,
            738929.0,
            738929.0,
            738925.0,
            738925.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            2.0,
            0.0,
            0.0,
            2.0,
            2.0,
            2.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738932.0,
            738932.0,
            738936.0,
            738936.0,
            738932.0,
            738932.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738939.0,
            738939.0,
            738943.0,
            738943.0,
            738939.0,
            738939.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            4.0,
            0.0,
            0.0,
            4.0,
            4.0,
            4.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738946.0,
            738946.0,
            738950.0,
            738950.0,
            738946.0,
            738946.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738953.0,
            738953.0,
            738957.0,
            738957.0,
            738953.0,
            738953.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738960.0,
            738960.0,
            738964.0,
            738964.0,
            738960.0,
            738960.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738967.0,
            738967.0,
            738971.0,
            738971.0,
            738967.0,
            738967.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738974.0,
            738974.0,
            738978.0,
            738978.0,
            738974.0,
            738974.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738981.0,
            738981.0,
            738985.0,
            738985.0,
            738981.0,
            738981.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738988.0,
            738988.0,
            738992.0,
            738992.0,
            738988.0,
            738988.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            738995.0,
            738995.0,
            738999.0,
            738999.0,
            738995.0,
            738995.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739002.0,
            739002.0,
            739006.0,
            739006.0,
            739002.0,
            739002.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739009.0,
            739009.0,
            739013.0,
            739013.0,
            739009.0,
            739009.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            3.0,
            0.0,
            0.0,
            3.0,
            3.0,
            3.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739016.0,
            739016.0,
            739020.0,
            739020.0,
            739016.0,
            739016.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739023.0,
            739023.0,
            739027.0,
            739027.0,
            739023.0,
            739023.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739030.0,
            739030.0,
            739034.0,
            739034.0,
            739030.0,
            739030.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739037.0,
            739037.0,
            739041.0,
            739041.0,
            739037.0,
            739037.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739044.0,
            739044.0,
            739048.0,
            739048.0,
            739044.0,
            739044.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            4.0,
            0.0,
            0.0,
            4.0,
            4.0,
            4.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739051.0,
            739051.0,
            739055.0,
            739055.0,
            739051.0,
            739051.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739058.0,
            739058.0,
            739062.0,
            739062.0,
            739058.0,
            739058.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739065.0,
            739065.0,
            739069.0,
            739069.0,
            739065.0,
            739065.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            3.0,
            0.0,
            0.0,
            3.0,
            3.0,
            3.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739072.0,
            739072.0,
            739076.0,
            739076.0,
            739072.0,
            739072.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739079.0,
            739079.0,
            739083.0,
            739083.0,
            739079.0,
            739079.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739086.0,
            739086.0,
            739090.0,
            739090.0,
            739086.0,
            739086.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739093.0,
            739093.0,
            739097.0,
            739097.0,
            739093.0,
            739093.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739100.0,
            739100.0,
            739104.0,
            739104.0,
            739100.0,
            739100.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739107.0,
            739107.0,
            739111.0,
            739111.0,
            739107.0,
            739107.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            2.0,
            0.0,
            0.0,
            2.0,
            2.0,
            2.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739114.0,
            739114.0,
            739118.0,
            739118.0,
            739114.0,
            739114.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            2.0,
            0.0,
            0.0,
            2.0,
            2.0,
            2.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739121.0,
            739121.0,
            739125.0,
            739125.0,
            739121.0,
            739121.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739128.0,
            739128.0,
            739132.0,
            739132.0,
            739128.0,
            739128.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            4.0,
            0.0,
            0.0,
            4.0,
            4.0,
            4.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739135.0,
            739135.0,
            739139.0,
            739139.0,
            739135.0,
            739135.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739142.0,
            739142.0,
            739146.0,
            739146.0,
            739142.0,
            739142.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739149.0,
            739149.0,
            739153.0,
            739153.0,
            739149.0,
            739149.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739156.0,
            739156.0,
            739160.0,
            739160.0,
            739156.0,
            739156.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739163.0,
            739163.0,
            739167.0,
            739167.0,
            739163.0,
            739163.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            2.0,
            0.0,
            0.0,
            2.0,
            2.0,
            2.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739170.0,
            739170.0,
            739174.0,
            739174.0,
            739170.0,
            739170.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739177.0,
            739177.0,
            739181.0,
            739181.0,
            739177.0,
            739177.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739184.0,
            739184.0,
            739188.0,
            739188.0,
            739184.0,
            739184.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739191.0,
            739191.0,
            739195.0,
            739195.0,
            739191.0,
            739191.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            2.0,
            0.0,
            0.0,
            2.0,
            2.0,
            2.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739198.0,
            739198.0,
            739202.0,
            739202.0,
            739198.0,
            739198.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739205.0,
            739205.0,
            739209.0,
            739209.0,
            739205.0,
            739205.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739212.0,
            739212.0,
            739216.0,
            739216.0,
            739212.0,
            739212.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739219.0,
            739219.0,
            739223.0,
            739223.0,
            739219.0,
            739219.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739226.0,
            739226.0,
            739230.0,
            739230.0,
            739226.0,
            739226.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739233.0,
            739233.0,
            739237.0,
            739237.0,
            739233.0,
            739233.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739240.0,
            739240.0,
            739244.0,
            739244.0,
            739240.0,
            739240.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            2.0,
            0.0,
            0.0,
            2.0,
            2.0,
            2.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739247.0,
            739247.0,
            739251.0,
            739251.0,
            739247.0,
            739247.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739254.0,
            739254.0,
            739258.0,
            739258.0,
            739254.0,
            739254.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739261.0,
            739261.0,
            739265.0,
            739265.0,
            739261.0,
            739261.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739268.0,
            739268.0,
            739272.0,
            739272.0,
            739268.0,
            739268.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739275.0,
            739275.0,
            739279.0,
            739279.0,
            739275.0,
            739275.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739282.0,
            739282.0,
            739286.0,
            739286.0,
            739282.0,
            739282.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739289.0,
            739289.0,
            739293.0,
            739293.0,
            739289.0,
            739289.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739296.0,
            739296.0,
            739300.0,
            739300.0,
            739296.0,
            739296.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739303.0,
            739303.0,
            739307.0,
            739307.0,
            739303.0,
            739303.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739310.0,
            739310.0,
            739314.0,
            739314.0,
            739310.0,
            739310.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            4.0,
            0.0,
            0.0,
            4.0,
            4.0,
            4.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739317.0,
            739317.0,
            739321.0,
            739321.0,
            739317.0,
            739317.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739324.0,
            739324.0,
            739328.0,
            739328.0,
            739324.0,
            739324.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739331.0,
            739331.0,
            739335.0,
            739335.0,
            739331.0,
            739331.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739338.0,
            739338.0,
            739342.0,
            739342.0,
            739338.0,
            739338.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739345.0,
            739345.0,
            739349.0,
            739349.0,
            739345.0,
            739345.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            2.0,
            0.0,
            0.0,
            2.0,
            2.0,
            2.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739352.0,
            739352.0,
            739356.0,
            739356.0,
            739352.0,
            739352.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            2.0,
            0.0,
            0.0,
            2.0,
            2.0,
            2.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739359.0,
            739359.0,
            739363.0,
            739363.0,
            739359.0,
            739359.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739366.0,
            739366.0,
            739370.0,
            739370.0,
            739366.0,
            739366.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739373.0,
            739373.0,
            739377.0,
            739377.0,
            739373.0,
            739373.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739380.0,
            739380.0,
            739384.0,
            739384.0,
            739380.0,
            739380.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739387.0,
            739387.0,
            739391.0,
            739391.0,
            739387.0,
            739387.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            2.0,
            0.0,
            0.0,
            2.0,
            2.0,
            2.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739394.0,
            739394.0,
            739398.0,
            739398.0,
            739394.0,
            739394.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739401.0,
            739401.0,
            739405.0,
            739405.0,
            739401.0,
            739401.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739408.0,
            739408.0,
            739412.0,
            739412.0,
            739408.0,
            739408.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            2.0,
            0.0,
            0.0,
            2.0,
            2.0,
            2.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739415.0,
            739415.0,
            739419.0,
            739419.0,
            739415.0,
            739415.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739422.0,
            739422.0,
            739426.0,
            739426.0,
            739422.0,
            739422.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            4.0,
            0.0,
            0.0,
            4.0,
            4.0,
            4.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739429.0,
            739429.0,
            739433.0,
            739433.0,
            739429.0,
            739429.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739436.0,
            739436.0,
            739440.0,
            739440.0,
            739436.0,
            739436.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            4.0,
            0.0,
            0.0,
            4.0,
            4.0,
            4.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739443.0,
            739443.0,
            739447.0,
            739447.0,
            739443.0,
            739443.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739450.0,
            739450.0,
            739454.0,
            739454.0,
            739450.0,
            739450.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            3.0,
            0.0,
            0.0,
            3.0,
            3.0,
            3.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739457.0,
            739457.0,
            739461.0,
            739461.0,
            739457.0,
            739457.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "fill": "tozeroy",
        "yaxis": "y2",
        "x": [
            739462.0,
            739462.0,
            739466.0,
            739466.0,
            739462.0,
            739462.0
        ],
        "showlegend": false,
        "mode": "lines",
        "fillcolor": "rgba(51, 102, 178, 0.700)",
        "name": "成交量",
        "legendgroup": "成交量",
        "line": {
            "color": "rgba(0, 0, 0, 0.700)",
            "dash": "solid",
            "width": 1
        },
        "y": [
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    },
    {
        "xaxis": "x2",
        "colorbar": {
            "y": 0.14547278269903766,
            "title": "",
            "len": 0.21513082349081364,
            "x": 0.996719160104987
        },
        "yaxis": "y2",
        "x": [
            738773.0,
            738780.0,
            738787.0,
            738794.0,
            738801.0,
            738808.0,
            738815.0,
            738822.0,
            738829.0,
            738836.0,
            738843.0,
            738850.0,
            738857.0,
            738864.0,
            738871.0,
            738878.0,
            738885.0,
            738892.0,
            738899.0,
            738906.0,
            738913.0,
            738920.0,
            738927.0,
            738934.0,
            738941.0,
            738948.0,
            738955.0,
            738962.0,
            738969.0,
            738976.0,
            738983.0,
            738990.0,
            738997.0,
            739004.0,
            739011.0,
            739018.0,
            739025.0,
            739032.0,
            739039.0,
            739046.0,
            739053.0,
            739060.0,
            739067.0,
            739074.0,
            739081.0,
            739088.0,
            739095.0,
            739102.0,
            739109.0,
            739116.0,
            739123.0,
            739130.0,
            739137.0,
            739144.0,
            739151.0,
            739158.0,
            739165.0,
            739172.0,
            739179.0,
            739186.0,
            739193.0,
            739200.0,
            739207.0,
            739214.0,
            739221.0,
            739228.0,
            739235.0,
            739242.0,
            739249.0,
            739256.0,
            739263.0,
            739270.0,
            739277.0,
            739284.0,
            739291.0,
            739298.0,
            739305.0,
            739312.0,
            739319.0,
            739326.0,
            739333.0,
            739340.0,
            739347.0,
            739354.0,
            739361.0,
            739368.0,
            739375.0,
            739382.0,
            739389.0,
            739396.0,
            739403.0,
            739410.0,
            739417.0,
            739424.0,
            739431.0,
            739438.0,
            739445.0,
            739452.0,
            739459.0,
            739464.0
        ],
        "showlegend": false,
        "mode": "markers",
        "name": "成交量",
        "legendgroup": "成交量",
        "marker": {
            "symbol": "circle",
            "color": "rgba(51, 102, 178, 0.000)",
            "line": {
                "color": "rgba(0, 0, 0, 0.000)",
                "width": 1
            },
            "size": 0
        },
        "y": [
            1.0,
            7.0,
            2.0,
            0.0,
            0.0,
            0.0,
            1.0,
            0.0,
            0.0,
            2.0,
            0.0,
            1.0,
            0.0,
            1.0,
            0.0,
            0.0,
            1.0,
            1.0,
            0.0,
            2.0,
            0.0,
            1.0,
            2.0,
            0.0,
            4.0,
            1.0,
            1.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            3.0,
            0.0,
            1.0,
            0.0,
            0.0,
            4.0,
            1.0,
            0.0,
            3.0,
            1.0,
            0.0,
            1.0,
            0.0,
            0.0,
            2.0,
            2.0,
            1.0,
            4.0,
            1.0,
            0.0,
            0.0,
            0.0,
            2.0,
            0.0,
            1.0,
            0.0,
            2.0,
            0.0,
            1.0,
            1.0,
            0.0,
            0.0,
            1.0,
            2.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            1.0,
            0.0,
            0.0,
            4.0,
            1.0,
            0.0,
            1.0,
            0.0,
            2.0,
            2.0,
            1.0,
            1.0,
            0.0,
            0.0,
            2.0,
            0.0,
            1.0,
            2.0,
            1.0,
            4.0,
            0.0,
            4.0,
            0.0,
            3.0,
            1.0,
            1.0
        ],
        "type": "scatter"
    }
]
, {
    "showlegend": true,
    "paper_bgcolor": "rgba(255, 255, 255, 1.000)",
    "xaxis1": {
        "showticklabels": true,
        "gridwidth": 1,
        "tickvals": [
            738886.0,
            739252.0
        ],
        "range": [
            738752.27,
            739484.73
        ],
        "domain": [
            0.04494750656167979,
            0.996719160104987
        ],
        "mirror": false,
        "tickangle": 0,
        "showline": true,
        "ticktext": [
            "2024-01-01",
            "2025-01-01"
        ],
        "zeroline": false,
        "tickfont": {
            "color": "rgba(0, 0, 0, 1.000)",
            "family": "Microsoft YaHei, SimHei, Arial Unicode MS, sans-serif",
            "size": 14
        },
        "zerolinecolor": "rgba(0, 0, 0, 1.000)",
        "anchor": "y1",
        "visible": true,
        "ticks": "inside",
        "tickmode": "array",
        "linecolor": "rgba(0, 0, 0, 1.000)",
        "showgrid": true,
        "title": "日期",
        "gridcolor": "rgba(211, 211, 211, 0.300)",
        "titlefont": {
            "color": "rgba(0, 0, 0, 1.000)",
            "family": "Microsoft YaHei, SimHei, Arial Unicode MS, sans-serif",
            "size": 17
        },
        "tickcolor": "rgb(0, 0, 0)",
        "type": "-"
    },
    "legend": {
        "yanchor": "auto",
        "xanchor": "auto",
        "bordercolor": "rgba(0, 0, 0, 1.000)",
        "bgcolor": "rgba(255, 255, 255, 1.000)",
        "borderwidth": 1,
        "tracegroupgap": 0,
        "y": 1.0,
        "font": {
            "color": "rgba(0, 0, 0, 1.000)",
            "family": "Microsoft YaHei, SimHei, Arial Unicode MS, sans-serif",
            "size": 15
        },
        "title": {
            "font": {
                "color": "rgba(0, 0, 0, 1.000)",
                "family": "Microsoft YaHei, SimHei, Arial Unicode MS, sans-serif",
                "size": 15
            },
            "text": ""
        },
        "traceorder": "normal",
        "x": 1.0
    },
    "height": 800,
    "yaxis2": {
        "showticklabels": true,
        "gridwidth": 1,
        "tickvals": [
            0.0,
            1.0,
            2.0,
            3.0,
            4.0,
            5.0,
            6.0,
            7.0
        ],
        "range": [
            0.0,
            7.0
        ],
        "domain": [
            0.03790737095363082,
            0.2530381944444445
        ],
        "mirror": false,
        "tickangle": 0,
        "showline": true,
        "ticktext": [
            "0",
            "1",
            "2",
            "3",
            "4",
            "5",
            "6",
            "7"
        ],
        "zeroline": false,
        "tickfont": {
            "color": "rgba(0, 0, 0, 1.000)",
            "family": "Microsoft YaHei, SimHei, Arial Unicode MS, sans-serif",
            "size": 13
        },
        "zerolinecolor": "rgba(0, 0, 0, 1.000)",
        "anchor": "x2",
        "visible": true,
        "ticks": "inside",
        "tickmode": "array",
        "linecolor": "rgba(0, 0, 0, 1.000)",
        "showgrid": true,
        "title": "成交量",
        "gridcolor": "rgba(211, 211, 211, 0.300)",
        "titlefont": {
            "color": "rgba(0, 0, 0, 1.000)",
            "family": "Microsoft YaHei, SimHei, Arial Unicode MS, sans-serif",
            "size": 14
        },
        "tickcolor": "rgb(0, 0, 0)",
        "type": "-"
    },
    "xaxis2": {
        "showticklabels": true,
        "gridwidth": 1,
        "tickvals": [
            738886.0,
            739252.0
        ],
        "range": [
            738728.049,
            739508.951
        ],
        "domain": [
            0.04494750656167979,
            0.996719160104987
        ],
        "mirror": false,
        "tickangle": 0,
        "showline": true,
        "ticktext": [
            "2024-01-01",
            "2025-01-01"
        ],
        "zeroline": false,
        "tickfont": {
            "color": "rgba(0, 0, 0, 1.000)",
            "family": "Microsoft YaHei, SimHei, Arial Unicode MS, sans-serif",
            "size": 13
        },
        "zerolinecolor": "rgba(0, 0, 0, 1.000)",
        "anchor": "y2",
        "visible": true,
        "ticks": "inside",
        "tickmode": "array",
        "linecolor": "rgba(0, 0, 0, 1.000)",
        "showgrid": true,
        "title": "日期",
        "gridcolor": "rgba(211, 211, 211, 0.300)",
        "titlefont": {
            "color": "rgba(0, 0, 0, 1.000)",
            "family": "Microsoft YaHei, SimHei, Arial Unicode MS, sans-serif",
            "size": 14
        },
        "tickcolor": "rgb(0, 0, 0)",
        "type": "-"
    },
    "yaxis1": {
        "showticklabels": true,
        "gridwidth": 1,
        "tickvals": [
            -15.0,
            -10.0,
            -5.0,
            0.0
        ],
        "range": [
            -18.782448868292228,
            3.8164587637473684
        ],
        "domain": [
            0.3219084919072616,
            0.9673009623797024
        ],
        "mirror": false,
        "tickangle": 0,
        "showline": true,
        "ticktext": [
            "-15",
            "-10",
            "-5",
            "0"
        ],
        "zeroline": false,
        "tickfont": {
            "color": "rgba(0, 0, 0, 1.000)",
            "family": "Microsoft YaHei, SimHei, Arial Unicode MS, sans-serif",
            "size": 14
        },
        "zerolinecolor": "rgba(0, 0, 0, 1.000)",
        "anchor": "x1",
        "visible": true,
        "ticks": "inside",
        "tickmode": "array",
        "linecolor": "rgba(0, 0, 0, 1.000)",
        "showgrid": true,
        "title": "價格",
        "gridcolor": "rgba(211, 211, 211, 0.300)",
        "titlefont": {
            "color": "rgba(0, 0, 0, 1.000)",
            "family": "Microsoft YaHei, SimHei, Arial Unicode MS, sans-serif",
            "size": 17
        },
        "tickcolor": "rgb(0, 0, 0)",
        "type": "-"
    },
    "annotations": [
        {
            "yanchor": "top",
            "xanchor": "center",
            "rotation": -0.0,
            "y": 1.0,
            "font": {
                "color": "rgba(0, 0, 0, 1.000)",
                "family": "Microsoft YaHei, SimHei, Arial Unicode MS, sans-serif",
                "size": 22
            },
            "yref": "paper",
            "showarrow": false,
            "text": "fan5_13,18,17,02,34 週線 蠟燭圖",
            "xref": "paper",
            "x": 0.5208333333333333
        },
        {
            "yanchor": "top",
            "xanchor": "center",
            "rotation": -0.0,
            "y": 0.27879278762029747,
            "font": {
                "color": "rgba(0, 0, 0, 1.000)",
                "family": "Microsoft YaHei, SimHei, Arial Unicode MS, sans-serif",
                "size": 17
            },
            "yref": "paper",
            "showarrow": false,
            "text": "成交量",
            "xref": "paper",
            "x": 0.5208333333333333
        }
    ],
    "plot_bgcolor": "rgba(255, 255, 255, 1.000)",
    "margin": {
        "l": 0,
        "b": 20,
        "r": 0,
        "t": 20
    },
    "width": 1200
}
);
        
    }
    </script>
    <script src="https://requirejs.org/docs/release/2.3.7/minified/require.js" onload="plots_jl_plotly_25f0076a_d3f3_4097_b200_51ab63c22344()"></script>

    </div>
</body>
</html>