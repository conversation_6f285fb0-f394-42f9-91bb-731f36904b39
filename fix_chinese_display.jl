#!/usr/bin/env julia

"""
AmiBroker 圖表模擬器中文顯示修復工具
解決圖表中文顯示不完全的問題
"""

println("🔧 AmiBroker 圖表模擬器中文顯示修復工具")
println("="^60)

# 檢查當前環境
println("\n📋 環境檢查：")

# 1. 檢查 Julia 版本
println("Julia 版本：$(VERSION)")

# 2. 檢查 Plots.jl 是否安裝
try
    using Plots
    println("✅ Plots.jl 已安裝")
    
    # 檢查可用後端
    available_backends = []
    
    # 檢查 PlotlyJS
    try
        using PlotlyJS
        push!(available_backends, "PlotlyJS")
        println("✅ PlotlyJS 後端可用（推薦用於中文顯示）")
    catch
        println("❌ PlotlyJS 後端不可用")
    end
    
    # 檢查 GR
    try
        using GR
        push!(available_backends, "GR")
        println("✅ GR 後端可用（中文支持有限）")
    catch
        println("❌ GR 後端不可用")
    end
    
    println("可用後端：$(join(available_backends, ", "))")
    
catch e
    println("❌ Plots.jl 未安裝：$e")
    println("請先安裝：julia --project=. -e \"using Pkg; Pkg.add(\\\"Plots\\\")\"")
    exit(1)
end

# 3. 檢查系統字體
println("\n🔤 字體檢查：")
if Sys.iswindows()
    println("Windows 系統檢測到")
    println("推薦字體：Microsoft YaHei, SimHei, SimSun")
elseif Sys.islinux()
    println("Linux 系統檢測到")
    println("推薦字體：WenQuanYi Micro Hei, Noto Sans CJK SC")
elseif Sys.isapple()
    println("macOS 系統檢測到")
    println("推薦字體：PingFang SC, Hiragino Sans GB")
end

# 4. 修復方案
println("\n🛠️  應用修復方案：")

# 載入修復後的模組
include("src/amibroker_chart_simple.jl")

println("當前後端：$CURRENT_BACKEND")
println("Plots 可用性：$PLOTS_AVAILABLE")

if PLOTS_AVAILABLE
    # 強制使用 PlotlyJS 後端（如果可用）
    try
        using PlotlyJS
        plotlyjs()
        println("✅ 已切換到 PlotlyJS 後端")
        
        # 設置最佳中文字體
        if Sys.iswindows()
            font_family = "Microsoft YaHei, SimHei, Arial Unicode MS, sans-serif"
        elseif Sys.islinux()
            font_family = "WenQuanYi Micro Hei, Noto Sans CJK SC, DejaVu Sans, sans-serif"
        elseif Sys.isapple()
            font_family = "PingFang SC, Hiragino Sans GB, Arial Unicode MS, sans-serif"
        else
            font_family = "Arial Unicode MS, sans-serif"
        end
        
        # 應用字體設置
        default(
            fontfamily=font_family,
            titlefontsize=16,
            guidefontsize=12,
            tickfontsize=10,
            legendfontsize=11
        )
        
        println("✅ 中文字體設置已優化")
        println("字體族：$font_family")
        
    catch e
        println("⚠️  PlotlyJS 設置失敗，使用 GR 後端：$e")
        gr()
        
        # GR 後端的字體設置
        default(
            fontfamily="Arial",
            titlefontsize=14,
            guidefontsize=11,
            tickfontsize=9,
            legendfontsize=10
        )
        println("✅ GR 後端字體設置完成（中文支持有限）")
    end
    
    # 測試圖表生成
    println("\n📊 測試圖表生成：")
    try
        # 生成測試圖表
        show_simple_enhanced_chart("fan5_01", "weekly", "candlestick")
        println("✅ 測試圖表生成成功")
        
        # 檢查生成的圖表文件
        charts_dir = "charts"
        if isdir(charts_dir)
            files = readdir(charts_dir)
            html_files = filter(f -> endswith(f, ".html"), files)
            if !isempty(html_files)
                latest_file = sort(html_files)[end]
                file_path = joinpath(charts_dir, latest_file)
                
                # 檢查中文字符
                content = read(file_path, String)
                chinese_test = ["週線", "蠟燭圖", "成交量", "日期", "價格", "上漲", "下跌"]
                found_chinese = filter(char -> occursin(char, content), chinese_test)
                
                println("📄 最新圖表：$latest_file")
                println("✅ 中文字符檢測：$(join(found_chinese, ", "))")
                
                if length(found_chinese) >= 4
                    println("🎉 中文顯示修復成功！")
                else
                    println("⚠️  中文顯示仍有問題，建議檢查系統字體")
                end
            end
        end
        
    catch e
        println("❌ 測試圖表生成失敗：$e")
    end
    
else
    println("❌ Plots.jl 不可用，無法進行修復")
end

# 5. 修復建議
println("\n💡 修復建議和最佳實踐：")
println("1. 優先使用 PlotlyJS 後端：plotlyjs()")
println("2. 安裝系統中文字體（Windows: 微軟雅黑，Linux: 文泉驛微米黑）")
println("3. 在圖表生成前調用 setup_chinese_fonts()")
println("4. 使用 HTML 格式保存圖表以獲得最佳顯示效果")

println("\n📦 推薦安裝命令：")
println("julia --project=. -e \"using Pkg; Pkg.add([\\\"PlotlyJS\\\", \\\"WebIO\\\"])\"")

println("\n🎯 使用示例：")
println("```julia")
println("using Plots")
println("plotlyjs()  # 使用 PlotlyJS 後端")
println("setup_chinese_fonts()  # 設置中文字體")
println("show_simple_enhanced_chart(\\\"fan5_01\\\", \\\"weekly\\\", \\\"candlestick\\\")")
println("```")

println("\n✅ 修復工具執行完成！")
